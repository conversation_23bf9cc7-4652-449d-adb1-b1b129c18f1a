// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AgentModels.generated.h"

/**
 * Data structure for agent information
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentData
{
    GENERATED_BODY()

    /** The unique identifier for the agent */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Id;

    /** The agent's name */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Name;

    /** The agent's age */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    int32 Age;

    /** The agent's traits */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Traits;

    /** The agent's status */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Status;

    /** The agent's summary */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Summary;

    /** When the summary was last refreshed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString LastRefreshed;
};

/**
 * Data structure for agent creation request
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentCreateRequest
{
    GENERATED_BODY()

    /** The agent's name */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Name;

    /** The agent's age */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    int32 Age;

    /** The agent's traits */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Traits;

    /** The agent's status */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Status;

    // /** The language model to use */ // Removed LlmModel
    // UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    // FString LlmModel;

    /** When to trigger reflection */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    float ReflectionThreshold;

    /** Weight for memory importance */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    float ImportanceWeight;

    /** Enable verbose logging */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    bool bVerbose;

    /** Initial memories or knowledge texts for the agent's vector store */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    TArray<FString> InitialMemories;
};

/**
 * Data structure for agent reaction request
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentReactionRequest
{
    GENERATED_BODY()

    /** The observation for the agent to react to */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Observation;

    /** The current time (ISO format) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString CurrentTime;
};

/**
 * Data structure for agent reaction response
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentReactionResponse
{
    GENERATED_BODY()

    /** Whether the reaction is a dialogue */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    bool bIsDialogue;

    /** The agent's reaction */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Reaction;
};

/**
 * Data structure for agent dialogue response
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentDialogueResponse
{
    GENERATED_BODY()

    /** Whether the dialogue is continuing */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    bool bIsDialogueContinuing;

    /** The agent's response */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|Agent")
    FString Response;
};
