#!/usr/bin/env python3
"""
Test script to verify timeout fixes are working correctly.

This script tests:
1. Health check endpoint
2. Request timing middleware
3. Timeout handling
4. Performance monitoring
"""

import asyncio
import aiohttp
import time
import json
import sys
from typing import Dict, Any

class TimeoutTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self) -> Dict[str, Any]:
        """Test the health check endpoint."""
        print("🔍 Testing health check endpoint...")
        
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                data = await response.json()
                
                if response.status == 200:
                    print("✅ Health check passed")
                    print(f"   Status: {data.get('status')}")
                    print(f"   Timeout config: {data.get('configuration', {}).get('timeout_seconds')}s")
                    print(f"   Warning threshold: {data.get('configuration', {}).get('warning_threshold')}s")
                    return data
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return {"error": f"HTTP {response.status}"}
        
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return {"error": str(e)}
    
    async def test_basic_request(self) -> Dict[str, Any]:
        """Test a basic request to verify timing headers."""
        print("\n🔍 Testing basic request timing...")
        
        try:
            start_time = time.time()
            async with self.session.get(f"{self.base_url}/") as response:
                end_time = time.time()
                data = await response.json()
                
                process_time = response.headers.get('X-Process-Time')
                actual_time = end_time - start_time
                
                print(f"✅ Basic request completed")
                print(f"   Status: {response.status}")
                print(f"   Process time header: {process_time}s")
                print(f"   Actual time: {actual_time:.3f}s")
                
                return {
                    "status": response.status,
                    "process_time": process_time,
                    "actual_time": actual_time,
                    "data": data
                }
        
        except Exception as e:
            print(f"❌ Basic request error: {e}")
            return {"error": str(e)}
    
    async def test_slow_request_simulation(self) -> Dict[str, Any]:
        """Simulate a slow request to test warning thresholds."""
        print("\n🔍 Testing slow request handling...")
        
        # This endpoint doesn't exist, but we can test the timeout middleware
        try:
            start_time = time.time()
            
            # Try to make a request that might be slow
            # We'll use a timeout to simulate what happens
            async with self.session.get(
                f"{self.base_url}/api/agents",
                timeout=aiohttp.ClientTimeout(total=5)  # 5 second timeout for testing
            ) as response:
                end_time = time.time()
                
                process_time = response.headers.get('X-Process-Time')
                actual_time = end_time - start_time
                
                print(f"✅ Request completed (might be fast)")
                print(f"   Status: {response.status}")
                print(f"   Process time: {process_time}s")
                print(f"   Actual time: {actual_time:.3f}s")
                
                return {
                    "status": response.status,
                    "process_time": process_time,
                    "actual_time": actual_time
                }
        
        except asyncio.TimeoutError:
            end_time = time.time()
            actual_time = end_time - start_time
            print(f"⚠️  Request timed out (expected for testing)")
            print(f"   Actual time: {actual_time:.3f}s")
            return {"timeout": True, "actual_time": actual_time}
        
        except Exception as e:
            print(f"❌ Slow request test error: {e}")
            return {"error": str(e)}
    
    async def test_concurrent_requests(self, num_requests: int = 5) -> Dict[str, Any]:
        """Test multiple concurrent requests."""
        print(f"\n🔍 Testing {num_requests} concurrent requests...")
        
        async def make_request(request_id: int):
            try:
                start_time = time.time()
                async with self.session.get(f"{self.base_url}/") as response:
                    end_time = time.time()
                    process_time = response.headers.get('X-Process-Time')
                    return {
                        "id": request_id,
                        "status": response.status,
                        "process_time": process_time,
                        "actual_time": end_time - start_time
                    }
            except Exception as e:
                return {"id": request_id, "error": str(e)}
        
        # Run concurrent requests
        tasks = [make_request(i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful = [r for r in results if isinstance(r, dict) and "error" not in r]
        failed = [r for r in results if isinstance(r, dict) and "error" in r]
        
        print(f"✅ Concurrent requests completed")
        print(f"   Successful: {len(successful)}")
        print(f"   Failed: {len(failed)}")
        
        if successful:
            avg_time = sum(float(r.get("process_time", 0) or 0) for r in successful) / len(successful)
            print(f"   Average process time: {avg_time:.3f}s")
        
        return {
            "total": num_requests,
            "successful": len(successful),
            "failed": len(failed),
            "results": results
        }

async def main():
    """Run all timeout tests."""
    print("🚀 Starting timeout fixes verification tests...\n")
    
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5000"
    print(f"Testing against: {base_url}")
    
    async with TimeoutTester(base_url) as tester:
        # Run all tests
        health_result = await tester.test_health_check()
        basic_result = await tester.test_basic_request()
        slow_result = await tester.test_slow_request_simulation()
        concurrent_result = await tester.test_concurrent_requests()
        
        # Summary
        print("\n" + "="*50)
        print("📊 TEST SUMMARY")
        print("="*50)
        
        print(f"Health Check: {'✅ PASS' if 'error' not in health_result else '❌ FAIL'}")
        print(f"Basic Request: {'✅ PASS' if 'error' not in basic_result else '❌ FAIL'}")
        print(f"Slow Request: {'✅ PASS' if 'error' not in slow_result else '❌ FAIL'}")
        print(f"Concurrent Requests: {'✅ PASS' if concurrent_result['failed'] == 0 else '⚠️  PARTIAL'}")
        
        # Configuration check
        if 'error' not in health_result:
            config = health_result.get('configuration', {})
            print(f"\n🔧 Configuration:")
            print(f"   Request Timeout: {config.get('timeout_seconds', 'N/A')}s")
            print(f"   Warning Threshold: {config.get('warning_threshold', 'N/A')}s")
            print(f"   Gunicorn Timeout: {config.get('gunicorn_timeout', 'N/A')}s")
            print(f"   Workers: {config.get('workers', 'N/A')}")
        
        print(f"\n✨ Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
