import os
from typing import Optional

# API configuration
API_PREFIX = "/api"
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

# LLM Configuration
OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
DEFAULT_MODEL_NAME: str = os.getenv("DEFAULT_MODEL_NAME", "gpt-4.1-nano")  # Default to gpt-4o
DEFAULT_EMBEDDINGS_MODEL: str = os.getenv("DEFAULT_EMBEDDINGS_MODEL", "text-embedding-3-small")  # Default embeddings model

# Memory Configuration
DEFAULT_MEMORY_IMPORTANCE_WEIGHT: float = float(os.getenv("MEMORY_IMPORTANCE_WEIGHT", "0.15"))
# If REFLECTION_THRESHOLD is set to a very high number (e.g., 999999) or None, reflection will be effectively disabled
# We use None as the default if the env var is set to "None" or "null", otherwise convert to float
reflection_threshold_str = os.getenv("REFLECTION_THRESHOLD", "0.5")
DEFAULT_REFLECTION_THRESHOLD: Optional[float] = None if reflection_threshold_str.lower() in ("none", "null") else float(reflection_threshold_str)
DEFAULT_MEMORY_K: int = int(os.getenv("MEMORY_K", "10"))

# Reflection Toggle
ENABLE_REFLECTION: bool = os.getenv("ENABLE_REFLECTION", "True").lower() in ("true", "1", "t")

# File Persistence Configuration
ENABLE_PERSISTENCE: bool = os.getenv("ENABLE_PERSISTENCE", "True").lower() in ("true", "1", "t")
DATA_DIR: str = os.getenv("DATA_DIR", "data")
DEFAULT_USER_ID: str = os.getenv("DEFAULT_USER_ID", "default")

# Redis and Task Queue Configuration
REDIS_URL: Optional[str] = os.getenv("REDIS_URL", "redis://localhost:6379/0")
DEFAULT_QUEUE_NAME: str = os.getenv("DEFAULT_QUEUE_NAME", "default")
