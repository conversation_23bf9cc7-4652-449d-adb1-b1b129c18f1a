"""
Service for handling application startup tasks.
"""
import logging
from typing import List, Dict, Any

from services import persistence_service, agent_service
from config import ENABLE_PERSISTENCE

logger = logging.getLogger(__name__)

def preload_agents() -> None:
    """
    Preload agents from disk into memory on application startup.
    This ensures that agents are immediately available when requested.
    """
    logger.info("Service (Startup): Starting agent preloading process.")
    if not ENABLE_PERSISTENCE:
        logger.info("Service (Startup): Persistence is disabled, skipping agent preloading.")
        return
    
    try:
        # Get all user IDs
        user_ids = persistence_service.get_all_user_ids() # This service call now has logging
        logger.info(f"Service (Startup): Found {len(user_ids)} user(s) with data directories: {user_ids}")
        
        total_agents_loaded = 0
        
        # For each user, load their agents
        for user_id in user_ids:
            logger.info(f"Service (Startup): Processing user_id: {user_id} for agent preloading.")
            # Get list of persisted agents for this user
            persisted_agents = persistence_service.list_persisted_agents(user_id) # This service call now has logging
            logger.info(f"Service (Startup): Found {len(persisted_agents)} persisted agent(s) for user_id: {user_id}.")
            
            # Load each agent
            for agent_metadata in persisted_agents:
                agent_id = agent_metadata["id"]
                logger.debug(f"Service (Startup): Attempting to preload agent ID: {agent_id} for user_id: {user_id}.")
                try:
                    # Load the agent into memory
                    agent_data = agent_service.get_agent(agent_id, user_id) # This service call now has logging
                    if agent_data:
                        total_agents_loaded += 1
                        logger.info(f"Service (Startup): Successfully preloaded agent ID: {agent_id} for user_id: {user_id}.")
                    else:
                        logger.warning(f"Service (Startup): Failed to preload agent ID: {agent_id} for user_id: {user_id} (get_agent returned None).")
                except Exception as e:
                    logger.error(f"Service (Startup): Error preloading agent ID: {agent_id} for user_id: {user_id}: {e}", exc_info=True)
        
        logger.info(f"Service (Startup): Agent preloading completed. Total agents loaded: {total_agents_loaded}.")
    except Exception as e:
        logger.error(f"Service (Startup): Critical error during agent preloading process: {e}", exc_info=True)
