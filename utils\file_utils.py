"""
Utility functions for file operations.
"""
import os
import json
import logging
import shutil
from typing import Dict, Any, Optional
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

# Global lock for file operations
file_lock = threading.Lock()

def ensure_directory_exists(directory_path: str) -> None:
    """
    Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory_path: Path to the directory
    """
    os.makedirs(directory_path, exist_ok=True)
    logger.debug(f"Ensured directory exists: {directory_path}")

def get_user_directory(user_id: str) -> str:
    """
    Get the directory for a specific user.
    
    Args:
        user_id: ID of the user
        
    Returns:
        Path to the user's directory
    """
    user_dir = os.path.join("data", "users", user_id)
    ensure_directory_exists(user_dir)
    return user_dir

def get_agent_directory(user_id: str, agent_id: str) -> str:
    """
    Get the directory for a specific agent.
    
    Args:
        user_id: ID of the user
        agent_id: ID of the agent
        
    Returns:
        Path to the agent's directory
    """
    agent_dir = os.path.join(get_user_directory(user_id), "agents", agent_id)
    ensure_directory_exists(agent_dir)
    return agent_dir

def save_json(file_path: str, data: Dict[str, Any]) -> None:
    """
    Save data to a JSON file with proper locking.
    
    Args:
        file_path: Path to the JSON file
        data: Data to save
    """
    with file_lock:
        # Create a temporary file
        temp_path = f"{file_path}.tmp"
        
        # Ensure the directory exists
        directory = os.path.dirname(file_path)
        ensure_directory_exists(directory)
        
        # Write to the temporary file
        with open(temp_path, 'w') as f:
            json.dump(data, f, default=json_serializer)
        
        # Rename the temporary file to the target file (atomic operation)
        os.replace(temp_path, file_path)
        
    logger.debug(f"Saved JSON to {file_path}")

def load_json(file_path: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Load data from a JSON file with proper locking.
    
    Args:
        file_path: Path to the JSON file
        default: Default value to return if the file doesn't exist
        
    Returns:
        Loaded data or default value
    """
    if not os.path.exists(file_path):
        return default if default is not None else {}
    
    with file_lock:
        with open(file_path, 'r') as f:
            return json.load(f)
    
def json_serializer(obj):
    """
    Custom JSON serializer for objects not serializable by default json code.
    
    Args:
        obj: Object to serialize
        
    Returns:
        Serialized object
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    
    # Add more custom serialization as needed
    raise TypeError(f"Type {type(obj)} not serializable")

def delete_directory(directory_path: str) -> bool:
    """
    Delete a directory and all its contents.
    
    Args:
        directory_path: Path to the directory to delete
        
    Returns:
        True if successful, False otherwise
    """
    if not os.path.exists(directory_path):
        return False
    
    with file_lock:
        try:
            shutil.rmtree(directory_path)
            return True
        except Exception as e:
            logger.error(f"Error deleting directory {directory_path}: {e}")
            return False
