from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class MemoryCreate(BaseModel):
    agent_id: str = Field(..., description="ID of the agent to add memory to")
    content: str = Field(..., description="Content of the memory to add", alias="memory_content")
    current_time: Optional[str] = Field(None, description="The time when the memory occurred (ISO format)")
    user_id: Optional[str] = Field(None, description="ID of the user who owns the agent")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "agent_id": "John-123-abcd1234",
                "memory_content": "<PERSON> went to the store and bought milk",
                "current_time": "2023-01-01T12:00:00"
            }
        }
    )


class BulkMemoryCreate(BaseModel):
    agent_id: str = Field(..., description="ID of the agent to add memories to")
    contents: List[str] = Field(..., description="List of memory contents to add")
    current_time: Optional[str] = Field(None, description="The time when the memories occurred (ISO format)")
    user_id: Optional[str] = Field(None, description="ID of the user who owns the agent")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "agent_id": "John-123-abcd1234",
                "contents": [
                    "John went to the store",
                    "John bought milk",
                    "John returned home"
                ],
                "current_time": "2023-01-01T12:00:00"
            }
        }
    )


class MemoryQuery(BaseModel):
    agent_id: str = Field(..., description="ID of the agent to query memories from")
    query: str = Field(..., description="Query to retrieve related memories")
    current_time: Optional[str] = Field(None, description="The current time (ISO format)")
    user_id: Optional[str] = Field(None, description="ID of the user who owns the agent")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "agent_id": "John-123-abcd1234",
                "query": "What did John buy at the store?",
                "current_time": "2023-01-01T13:00:00"
            }
        }
    )


class ReflectionRequest(BaseModel):
    agent_id: str = Field(..., description="ID of the agent to generate reflections for")
    current_time: Optional[str] = Field(None, description="The current time (ISO format)")
    user_id: Optional[str] = Field(None, description="ID of the user who owns the agent")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "agent_id": "John-123-abcd1234",
                "current_time": "2023-01-01T20:00:00"
            }
        }
    )


class Memory(BaseModel):
    content: str = Field(..., description="Content of the memory")
    created_at: datetime = Field(..., description="When the memory was created")
    importance: float = Field(..., description="Importance score of the memory")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "content": "John went to the store and bought milk",
                "created_at": "2023-01-01T12:00:00",
                "importance": 0.45
            }
        }
    )


class MemoryResponse(BaseModel):
    memories: List[Memory] = Field(..., description="List of retrieved memories")


class ReflectionResponse(BaseModel):
    reflections: List[str] = Field(..., description="List of generated reflections")
