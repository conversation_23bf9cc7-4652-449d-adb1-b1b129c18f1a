from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class AgentBase(BaseModel):
    name: str = Field(..., description="The character's name")
    age: Optional[int] = Field(None, description="The optional age of the character")
    traits: str = Field("N/A", description="Permanent traits to ascribe to the character")
    status: str = Field(..., description="The traits of the character you wish not to change")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "<PERSON>",
                "age": 25,
                "traits": "Kind, intelligent, curious",
                "status": "Healthy and energetic"
            }
        }
    )


class AgentCreate(AgentBase):
    # llm_model: Optional[str] = Field(None, description="The language model to use for this agent") # Removed
    reflection_threshold: Optional[float] = Field(
        None,
        description="When aggregate_importance exceeds this value, agent will reflect"
    )
    importance_weight: Optional[float] = Field(
        None,
        description="How much weight to assign to memory importance"
    )
    verbose: Optional[bool] = Field(False, description="Enable verbose logging")
    initial_memories: Optional[List[str]] = Field(None, description="Initial memories or texts for the agent's vector store")
    user_id: Optional[str] = Field(None, description="ID of the user who owns this agent")


class AgentUpdate(BaseModel):
    age: Optional[int] = None
    traits: Optional[str] = None
    status: Optional[str] = None
    # llm_model: Optional[str] = None # Removed
    reflection_threshold: Optional[float] = None
    importance_weight: Optional[float] = None
    verbose: Optional[bool] = None
    user_id: Optional[str] = None


class AgentSummary(BaseModel):
    summary: str = Field(..., description="Agent's self-summary")
    last_refreshed: datetime = Field(..., description="When the summary was last refreshed")


class AgentReaction(BaseModel):
    observation: str = Field(..., description="The observation for the agent to react to")
    current_time: Optional[str] = Field(None, description="The current time (ISO format)")
    user_id: Optional[str] = Field(None, description="ID of the user who owns the agent")


class DialogueResponse(BaseModel):
    is_dialogue_continuing: bool = Field(..., description="Whether the dialogue is continuing")
    response: str = Field(..., description="The agent's response")


class ReactionResponse(BaseModel):
    is_dialogue: bool = Field(..., description="Whether the reaction is a dialogue")
    reaction: str = Field(..., description="The agent's reaction")


class AgentResponse(AgentBase):
    id: str = Field(..., description="The unique identifier for the agent")
    summary: str = Field(..., description="The agent's current summary")
    last_refreshed: datetime = Field(..., description="When the summary was last refreshed")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "John-123-abcd1234",
                "name": "John",
                "age": 25,
                "traits": "Kind, intelligent, curious",
                "status": "Healthy and energetic",
                "summary": "John is a 25-year-old who values kindness and curiosity...",
                "last_refreshed": "2023-01-01T12:00:00"
            }
        }
    )


class AgentList(BaseModel):
    agents: List[AgentResponse] = Field(..., description="List of agents")
