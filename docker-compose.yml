version: '3.8'

services:
  # Main API service
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      # API Configuration
      DEBUG: "true"
      LOG_LEVEL: "DEBUG"
      
      # OpenAI API Configuration
      OPENAI_API_KEY: "${OPENAI_API_KEY}"
      DEFAULT_MODEL_NAME: "${DEFAULT_MODEL_NAME:-gpt-4o}"
      DEFAULT_EMBEDDINGS_MODEL: "${DEFAULT_EMBEDDINGS_MODEL:-text-embedding-3-small}"
      
      # Memory Configuration
      MEMORY_IMPORTANCE_WEIGHT: "${MEMORY_IMPORTANCE_WEIGHT:-0.15}"
      REFLECTION_THRESHOLD: "${REFLECTION_THRESHOLD:-0.5}"
      MEMORY_K: "${MEMORY_K:-10}"
      
      # File Persistence Configuration
      ENABLE_PERSISTENCE: "${ENABLE_PERSISTENCE:-true}"
      DATA_DIR: "/app/data"
      DEFAULT_USER_ID: "${DEFAULT_USER_ID:-default}"
      
      # Timeout Configuration
      GUNICORN_TIMEOUT: "300"
      GUNICORN_WORKERS: "1"  # Single worker for development
      GUNICORN_MAX_REQUESTS: "1000"
      GUNICORN_LOG_LEVEL: "debug"
      REQUEST_TIMEOUT: "240"
      REQUEST_WARNING_THRESHOLD: "60"
      
      # Reflection Configuration
      ENABLE_REFLECTION: "${ENABLE_REFLECTION:-true}"
      
      # Redis Configuration
      REDIS_URL: "redis://redis:6379/0"
      
      # Development specific
      PYTHONPATH: "/app"
      PYTHONUNBUFFERED: "1"
    
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
    
    depends_on:
      - redis
    
    # Use development command
    command: python main.py
    
    # Enable hot reloading
    stdin_open: true
    tty: true
    
    networks:
      - agent-network

  # Redis for caching and task queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - agent-network

  # Optional: Redis Commander for Redis GUI
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      REDIS_HOSTS: "local:redis:6379"
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - agent-network
    profiles:
      - tools

  # Optional: Production-like testing with Gunicorn
  api-prod:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5001:5000"
    environment:
      # Same as api service but with production settings
      DEBUG: "false"
      LOG_LEVEL: "INFO"
      OPENAI_API_KEY: "${OPENAI_API_KEY}"
      DEFAULT_MODEL_NAME: "${DEFAULT_MODEL_NAME:-gpt-4o}"
      DEFAULT_EMBEDDINGS_MODEL: "${DEFAULT_EMBEDDINGS_MODEL:-text-embedding-3-small}"
      MEMORY_IMPORTANCE_WEIGHT: "${MEMORY_IMPORTANCE_WEIGHT:-0.15}"
      REFLECTION_THRESHOLD: "${REFLECTION_THRESHOLD:-0.5}"
      MEMORY_K: "${MEMORY_K:-10}"
      ENABLE_PERSISTENCE: "${ENABLE_PERSISTENCE:-true}"
      DATA_DIR: "/app/data"
      DEFAULT_USER_ID: "${DEFAULT_USER_ID:-default}"
      GUNICORN_TIMEOUT: "300"
      GUNICORN_WORKERS: "2"
      GUNICORN_MAX_REQUESTS: "1000"
      GUNICORN_LOG_LEVEL: "info"
      REQUEST_TIMEOUT: "240"
      REQUEST_WARNING_THRESHOLD: "60"
      ENABLE_REFLECTION: "${ENABLE_REFLECTION:-true}"
      REDIS_URL: "redis://redis:6379/0"
    
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
    
    depends_on:
      - redis
    
    # Use production command
    command: gunicorn -c gunicorn.conf.py main:app
    
    networks:
      - agent-network
    
    profiles:
      - production

volumes:
  redis_data:

networks:
  agent-network:
    driver: bridge
