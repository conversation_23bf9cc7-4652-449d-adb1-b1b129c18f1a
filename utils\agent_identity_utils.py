"""
Utility functions for agent identity management, including sanitization and ID generation/parsing.
"""
import uuid
import re

def sanitize_name(name: str) -> str:
    """
    Sanitizes a raw agent name to be filesystem-friendly.
    Replaces non-alphanumeric characters (excluding underscores) with underscores.
    Collapses multiple consecutive underscores into a single underscore.

    Special handling for quotes in names like '<PERSON> "The Brew" Roberts' to ensure
    consistent sanitization.
    """
    if not name:
        return "unnamed_agent"

    # Special handling for names with quotes
    # First, replace quotes with a temporary marker that won't be affected by the next regex
    sanitized = name.replace('"', '_QUOTE_').replace("'", '_QUOTE_')

    # Replace non-alphanumeric (excluding underscore) with underscore
    sanitized = re.sub(r'[^\w_]', '_', sanitized)

    # Replace the temporary marker with underscore
    sanitized = sanitized.replace('_QUOTE_', '_')

    # Collapse multiple underscores
    sanitized = re.sub(r'_+', '_', sanitized)

    # Remove leading/trailing underscores if any (optional, but good for cleanliness)
    sanitized = sanitized.strip('_')

    # Ensure not empty after stripping
    if not sanitized:
        return "sanitized_agent" # Fallback if name was all special chars

    return sanitized

def generate_agent_id(name: str, user_id: str) -> str:
    """
    Generates a canonical agent ID.
    Format: "{sanitized_name}-{user_id_of_creator}-{uuid}"
    """
    sanitized = sanitize_name(name)
    agent_uuid = str(uuid.uuid4())
    return f"{sanitized}-{user_id}-{agent_uuid}"

def extract_user_id_from_agent_id(agent_id: str) -> str:
    """
    Extracts the user_id from a canonical agent_id.
    Assumes agent_id format: "sanitized_name-user_id-uuid"
    Also handles agent_id format with underscores: "sanitized_name_with_underscores-user_id-uuid"
    """
    # First, handle the case where the agent_id uses underscores instead of hyphens in the name part
    # Example: "Bob_The_Brew_Roberts-default-f817ee26-7521-43e9-bbb8-9de1a65da27b"
    # Convert to: "Bob_The_Brew_Roberts-default-f817ee26-7521-43e9-bbb8-9de1a65da27b"

    parts = agent_id.split('-')
    if len(parts) < 3: # Must have at least name, user_id, and one part for uuid
        raise ValueError(f"Invalid agent_id format for user_id extraction: {agent_id}. Expected 'name-user_id-uuid'.")

    # Robust way: find the last part that looks like a UUID.
    # UUID pattern: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
    uuid_pattern = re.compile(r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')

    # Iterate backwards from the end of parts to reconstruct potential UUIDs
    for i in range(1, min(6, len(parts))): # UUID has 5 segments, check up to 5
        potential_uuid_parts = parts[-i:]
        potential_uuid_str = "-".join(potential_uuid_parts)
        if uuid_pattern.match(potential_uuid_str) and len(potential_uuid_str) == 36:
            # Found the UUID. The user_id is the part before these segments.
            user_id_index = len(parts) - i - 1
            if user_id_index >= 0:
                return parts[user_id_index]
            else:
                # This case implies agent_id was just "user_id-uuid" or "uuid" which is invalid by our new format.
                raise ValueError(f"Invalid agent_id format: Cannot find user_id part before UUID in {agent_id}")

    raise ValueError(f"Could not extract user_id from agent_id: {agent_id}. UUID not found or format incorrect.")

def extract_name_from_agent_id(agent_id: str) -> str:
    """
    Extracts the sanitized_name part from a canonical agent_id.
    Also handles agent_id format with underscores: "sanitized_name_with_underscores-user_id-uuid"
    """
    parts = agent_id.split('-')
    if len(parts) < 3:
        raise ValueError(f"Invalid agent_id format for name extraction: {agent_id}. Expected 'name-user_id-uuid'.")

    uuid_pattern = re.compile(r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')

    # Find where the UUID starts
    uuid_start_index_in_parts = -1
    for i in range(1, min(6, len(parts))):
        potential_uuid_parts = parts[-i:]
        potential_uuid_str = "-".join(potential_uuid_parts)
        if uuid_pattern.match(potential_uuid_str) and len(potential_uuid_str) == 36:
            uuid_start_index_in_parts = len(parts) - i
            break

    if uuid_start_index_in_parts == -1:
        raise ValueError(f"Could not extract name from agent_id: {agent_id}. UUID not found.")

    # The user_id is at uuid_start_index_in_parts - 1
    # The name parts are everything before that
    if uuid_start_index_in_parts -1 <= 0 : # name part must exist
         raise ValueError(f"Could not extract name from agent_id: {agent_id}. No name part found before user_id and UUID.")

    name_parts = parts[:uuid_start_index_in_parts -1]
    return "-".join(name_parts)
