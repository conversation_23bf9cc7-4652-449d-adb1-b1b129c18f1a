import uuid
import logging
import os
import math # Added import for math.sqrt
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

import faiss # Added import
from langchain.retrievers import TimeWeightedVectorStoreRetriever
from langchain_community.vectorstores import FAISS
from langchain_community.docstore import InMemoryDocstore # Added import
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from core.custom_agent import CustomGenerativeAgent, CustomGenerativeAgentMemory
from core.generative_agents.memory import SafeTimeWeightedVectorStoreRetriever

from models import agents_store
from utils.llm_utils import get_llm, get_embeddings
from utils.date_utils import parse_datetime
from utils.file_utils import get_agent_directory
from utils.agent_identity_utils import extract_user_id_from_agent_id, generate_agent_id # Added generate_agent_id
from config import (
    DEFAULT_MEMORY_IMPORTANCE_WEIGHT,
    DEFAULT_REFLECTION_THRESHOLD,
    DEFAULT_MEMORY_K, # Added DEFAULT_MEMORY_K
    ENABLE_PERSISTENCE,
    DEFAULT_USER_ID
)
from services import persistence_service

logger = logging.getLogger(__name__)

# Renamed original function to make its synchronous nature clear,
# though it won't be directly used by the async-task-based route.
def create_agent_and_process_initial_memories_sync(
    name: str,
    status: str,
    age: Optional[int] = None,
    traits: str = "N/A",
    # llm_model: Optional[str] = None, # Removed llm_model
    reflection_threshold: Optional[float] = None,
    importance_weight: Optional[float] = None,
    verbose: bool = False,
    initial_memories: Optional[List[str]] = None,
    user_id: Optional[str] = None,
) -> Dict[str, Any]:
    logger.info(f"Service: Attempting to create agent '{name}' for user_id: {user_id or DEFAULT_USER_ID}")
    """
    Create a new generative agent.

    Args:
        name: The character's name
        status: The traits of the character you wish not to change
        age: The optional age of the character
        traits: Permanent traits to ascribe to the character
        # llm_model: The language model to use # Removed llm_model
        reflection_threshold: When to trigger reflection
        importance_weight: Weight for memory importance
        verbose: Enable verbose logging
        user_id: ID of the user who owns this agent (defaults to DEFAULT_USER_ID)

    Returns:
        Dictionary containing agent details
    """
    # Use the provided user_id or default
    user_id = user_id or DEFAULT_USER_ID

    # Generate a canonical agent ID using the utility function
    agent_id = generate_agent_id(name, user_id)
    logger.debug(f"Generated agent ID: {agent_id} for agent name: {name} using utility function.")

    # Create language model for the agent (will use default from config)
    llm = get_llm() # Removed model_name=llm_model

    # Create embedding model and vector store for the agent's memory
    embeddings = get_embeddings()

    # Create the memory for the agent with FAISS vector store
    # Initialize with an empty vector store. Initial memories will be added via agent.add_memory()
    # vector_store = FAISS.from_texts(
    # texts=[], embedding=embeddings
    # )

    # Determine embedding dimension
    # Embed a dummy query to get the dimension of the embeddings
    # Ensure get_embeddings() returns a valid embedding function object
    dummy_text_to_get_dimension = " "  # Using a single space as dummy text
    embedding_vectors = embeddings.embed_documents([dummy_text_to_get_dimension])
    if not embedding_vectors or not embedding_vectors[0]:
        logger.error("Could not determine embedding dimension. Embedding function might be misconfigured or returned empty results.")
        raise ValueError("Could not determine embedding dimension. Embedding function might be misconfigured.")
    d = len(embedding_vectors[0])

    # Create an empty FAISS index
    index = faiss.IndexFlatL2(d)

    # Create an empty docstore and index_to_docstore_id mapping
    docstore = InMemoryDocstore({})  # Initialize with an empty dict
    index_to_docstore_id = {}      # Initialize as an empty dictionary

    # Define a robust relevance score function
    def robust_relevance_score_fn(score: float) -> float:
        # Score is L2 distance for FAISS. OpenAI embeddings are unit normalized.
        # Max L2 distance for unit vectors is sqrt(2).
        # This clips the score to be between 0 and 1.
        return max(0.0, 1.0 - score / math.sqrt(2))

    # Instantiate the FAISS wrapper
    vector_store = FAISS(
        embedding_function=embeddings,  # Pass the embedding function itself
        index=index,
        docstore=docstore,
        index_to_docstore_id=index_to_docstore_id,
        relevance_score_fn=robust_relevance_score_fn # Added relevance_score_fn
    )

    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000, chunk_overlap=200
    )

    memory_retriever = SafeTimeWeightedVectorStoreRetriever(
        vectorstore=vector_store,
        decay_rate=0.01,
        k=DEFAULT_MEMORY_K, # Use DEFAULT_MEMORY_K
        search_kwargs={"k": DEFAULT_MEMORY_K}, # Use DEFAULT_MEMORY_K
    )

    # Set up memory with appropriate parameters
    memory = CustomGenerativeAgentMemory(
        llm=llm,
        memory_retriever=memory_retriever,
        reflection_threshold=reflection_threshold or DEFAULT_REFLECTION_THRESHOLD,
        importance_weight=importance_weight or DEFAULT_MEMORY_IMPORTANCE_WEIGHT,
        verbose=verbose,
    )

    # Create the generative agent
    agent = CustomGenerativeAgent(
        name=name,
        age=age,
        traits=traits,
        status=status,
        memory=memory,
        llm=llm,
        verbose=verbose,
    )

    # Add initial memories through the agent's memory system to ensure proper processing (e.g., importance scoring)
    if initial_memories:
        logger.info(f"Service: Adding {len(initial_memories)} initial memories for agent '{name}' (ID: {agent_id})...")
        for memory_content in initial_memories:
            try:
                agent.add_memory(memory_content) # This will use the LLM to score importance
            except Exception as e:
                logger.error(f"Service: Error adding initial memory '{memory_content[:50]}...' for agent '{name}' (ID: {agent_id}): {e}", exc_info=True)
                # Decide if we should continue or raise an error. For now, log and continue.
        logger.info(f"Service: Finished adding initial memories for agent '{name}' (ID: {agent_id}).")

    # Store the agent in memory
    agent_data = {
        "id": agent_id,
        "user_id": user_id, # user_id is now guaranteed to be set
        "name": name,
        "age": age,
        "traits": traits,
        "status": status,
        "agent": agent,
        "summary": "",
        "last_refreshed": datetime.now(),
    }

    agents_store[agent_id] = agent_data
    # logger.info(f"Created agent with ID: {agent_id} for user {user_id}") # This log is good, let's refine it slightly for consistency
    logger.info(f"Service (SYNC): Agent '{name}' (ID: {agent_id}) created successfully for user_id: {user_id}, including synchronous initial memory processing.")

    # Persist the agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE:
        persistence_service.save_agent(agent_data, user_id)

    return agent_data


def create_agent_core_data(
    name: str,
    status: str,
    age: Optional[int] = None,
    traits: str = "N/A",
    reflection_threshold: Optional[float] = None,
    importance_weight: Optional[float] = None,
    verbose: bool = False,
    user_id: Optional[str] = None,
    # initial_memories are NOT processed here; handled by background task
) -> Dict[str, Any]:
    logger.info(f"Service: Creating core data for agent '{name}' for user_id: {user_id or DEFAULT_USER_ID}")
    """
    Create the core structure of a new generative agent without processing initial memories.
    Initial memories should be enqueued by the caller to be processed by a background task.
    """
    user_id = user_id or DEFAULT_USER_ID
    # Generate a canonical agent ID using the utility function
    agent_id = generate_agent_id(name, user_id)
    logger.debug(f"Generated agent ID: {agent_id} for agent name: {name} using utility function for core data.")

    llm = get_llm()
    embeddings = get_embeddings()

    dummy_text_to_get_dimension = " "
    embedding_vectors = embeddings.embed_documents([dummy_text_to_get_dimension])
    if not embedding_vectors or not embedding_vectors[0]:
        logger.error("Could not determine embedding dimension.")
        raise ValueError("Could not determine embedding dimension.")
    d = len(embedding_vectors[0])

    index = faiss.IndexFlatL2(d)
    docstore = InMemoryDocstore({})
    index_to_docstore_id = {}

    # Define a robust relevance score function (can be defined at module level or passed if already defined)
    # Assuming robust_relevance_score_fn is defined at module level or accessible here
    # For clarity, if it's not at module level, it should be defined here or imported.
    # Re-defining or ensuring access to robust_relevance_score_fn:
    def robust_relevance_score_fn_core_data(score: float) -> float: # Renamed to avoid conflict if defined globally
        return max(0.0, 1.0 - score / math.sqrt(2))

    vector_store = FAISS(
        embedding_function=embeddings,
        index=index,
        docstore=docstore,
        index_to_docstore_id=index_to_docstore_id,
        relevance_score_fn=robust_relevance_score_fn_core_data # Added relevance_score_fn
    )

    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000, chunk_overlap=200
    )
    memory_retriever = SafeTimeWeightedVectorStoreRetriever(
        vectorstore=vector_store,
        decay_rate=0.01,
        k=DEFAULT_MEMORY_K,
        search_kwargs={"k": DEFAULT_MEMORY_K},
    )
    memory = CustomGenerativeAgentMemory(
        llm=llm,
        memory_retriever=memory_retriever,
        reflection_threshold=reflection_threshold or DEFAULT_REFLECTION_THRESHOLD,
        importance_weight=importance_weight or DEFAULT_MEMORY_IMPORTANCE_WEIGHT,
        verbose=verbose,
    )
    agent = CustomGenerativeAgent(
        name=name,
        age=age,
        traits=traits,
        status=status,
        memory=memory,
        llm=llm,
        verbose=verbose,
    )

    agent_data = {
        "id": agent_id,
        "user_id": user_id,
        "name": name,
        "age": age,
        "traits": traits,
        "status": status,
        "agent": agent, # The actual agent object
        "summary": "", # Summary will be generated later, possibly by a task or on demand
        "last_refreshed": datetime.now(), # Initial creation time
    }

    agents_store[agent_id] = agent_data
    logger.info(f"Service: Core data for agent '{name}' (ID: {agent_id}) created for user_id: {user_id}.")

    if ENABLE_PERSISTENCE:
        persistence_service.save_agent(agent_data, user_id)
        logger.info(f"Service: Core data for agent '{name}' (ID: {agent_id}) persisted for user_id: {user_id}.")

    # Return data that the route needs, especially the ID.
    # The 'agent' object itself might be too large or complex for direct return if not needed by route.
    # For now, returning a structure similar to what the route expects.
    return {
        "id": agent_id,
        "user_id": user_id,
        "name": name,
        "age": age,
        "traits": traits,
        "status": status,
        "summary": agent_data["summary"], # Will be empty initially
        "last_refreshed": agent_data["last_refreshed"] # Initial creation time
        # Note: The full 'agent' object is in agents_store and persisted
    }


def get_agent(agent_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get an agent by ID.

    Args:
        agent_id: ID of the agent to retrieve
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        Agent data dictionary or None if not found
    """
    logger.debug(f"Service: Attempting to get agent with ID: {agent_id} (user_id hint: {user_id})")
    # First check in-memory store
    agent_data = agents_store.get(agent_id)
    if agent_data:
        logger.debug(f"Service: Agent ID {agent_id} found in memory.")
        return agent_data

    # If not found in memory and persistence is enabled, try to load from disk
    if ENABLE_PERSISTENCE:
        # Extract user_id from agent_id if not provided, or validate if provided
        if not user_id:
            try:
                # Handle special case where agent_id has underscores in the name part
                # Example: "Bob_The_Brew_Roberts-default-f817ee26-7521-43e9-bbb8-9de1a65da27b"
                user_id = extract_user_id_from_agent_id(agent_id)
                logger.debug(f"Extracted user_id '{user_id}' from agent_id '{agent_id}' using utility function.")
            except ValueError as e:
                logger.warning(f"Could not extract user_id from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
                user_id = DEFAULT_USER_ID
        else:
            # If user_id is provided, ensure it's not None and use it.
            # Optionally, you could verify it against the agent_id's user_id part if strict consistency is needed.
            user_id = user_id or DEFAULT_USER_ID

        # Try to load agent from R2
        agent_data = persistence_service.load_agent_from_r2(user_id, agent_id)
        if agent_data:
            # Store the loaded agent in memory
            agents_store[agent_id] = agent_data
            logger.info(f"Service: Loaded agent {agent_id} from R2 for user_id {user_id} and stored in memory.")
            return agent_data
    logger.debug(f"Service: Agent ID {agent_id} not found in memory or R2 for user_id hint {user_id}.")
    return None


def list_agents(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    List all agents for a user.

    Args:
        user_id: ID of the user (defaults to DEFAULT_USER_ID)

    Returns:
        List of agent data dictionaries
    """
    effective_user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Service: Listing agents for user_id: {effective_user_id}")

    # Get in-memory agents for this user
    in_memory_agents = []
    for agent_data in agents_store.values():
        agent_user_id = agent_data.get("user_id", DEFAULT_USER_ID)
        if agent_user_id == effective_user_id: # Use effective_user_id
            in_memory_agents.append(agent_data)

    # If persistence is enabled, also get agents from disk
    if ENABLE_PERSISTENCE:
        persisted_agents = persistence_service.list_persisted_agents(effective_user_id) # Use effective_user_id

        # Filter out agents that are already in memory
        in_memory_ids = {agent["id"] for agent in in_memory_agents}
        disk_only_agent_ids = [agent["id"] for agent in persisted_agents if agent["id"] not in in_memory_ids]

        # Load agents from disk that aren't already in memory
        for agent_id_from_disk in disk_only_agent_ids: # Renamed to avoid conflict
            loaded_agent_data = get_agent(agent_id_from_disk, effective_user_id) # Use effective_user_id
            if loaded_agent_data:
                in_memory_agents.append(loaded_agent_data)
    logger.info(f"Service: Listed {len(in_memory_agents)} agents for user_id: {effective_user_id}.")
    return in_memory_agents


def update_agent(
    agent_id: str,
    age: Optional[int] = None,
    traits: Optional[str] = None,
    status: Optional[str] = None,
    # llm_model: Optional[str] = None, # Removed llm_model
    reflection_threshold: Optional[float] = None,
    importance_weight: Optional[float] = None,
    verbose: Optional[bool] = None,
    user_id: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Update an agent's details.

    Args:
        agent_id: ID of the agent to update
        age: New age for the agent
        traits: New traits for the agent
        status: New status for the agent
        # llm_model: New language model to use # Removed llm_model
        reflection_threshold: New reflection threshold
        importance_weight: New importance weight
        verbose: New verbose setting
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        Updated agent data dictionary or None if not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        try:
            effective_user_id = extract_user_id_from_agent_id(agent_id)
            logger.debug(f"Extracted user_id '{effective_user_id}' for update from agent_id '{agent_id}' using utility function.")
        except ValueError as e:
            logger.warning(f"Could not extract user_id for update from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID

    logger.info(f"Service: Attempting to update agent ID: {agent_id} for user_id: {effective_user_id}")

    agent_data = get_agent(agent_id, effective_user_id)
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for update (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]

    # Update agent attributes
    if age is not None:
        agent.age = age
        agent_data["age"] = age

    if traits is not None:
        agent.traits = traits
        agent_data["traits"] = traits

    if status is not None:
        agent.status = status
        agent_data["status"] = status

    if verbose is not None:
        agent.verbose = verbose

    # Update memory attributes
    if reflection_threshold is not None:
        agent.memory.reflection_threshold = reflection_threshold

    if importance_weight is not None:
        agent.memory.importance_weight = importance_weight

    # Update language model if needed - This section is removed as llm_model is no longer a parameter
    # if llm_model is not None:
    #     new_llm = get_llm(model_name=llm_model) # This would now use default
    #     agent.llm = new_llm
    #     agent.memory.llm = new_llm
    # If LLM needs to be updated for an existing agent (e.g. global default changed),
    # it would likely be part of a different "refresh" or "reconfigure" logic,
    # not a standard update. For now, the agent's LLM is set at creation.

    # Force refresh of summary
    agent.get_summary(force_refresh=True)
    agent_data["summary"] = agent.summary
    agent_data["last_refreshed"] = agent.last_refreshed

    # logger.info(f"Updated agent: {agent_id}") # This is good, let's add user_id
    logger.info(f"Service: Agent ID {agent_id} updated successfully for user_id: {effective_user_id}.")

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE:
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id

    return agent_data


def delete_agent(agent_id: str, user_id: Optional[str] = None) -> bool:
    """
    Delete an agent.

    Args:
        agent_id: ID of the agent to delete
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        True if the agent was deleted, False otherwise
    """
    effective_user_id = user_id
    if not effective_user_id:
        try:
            effective_user_id = extract_user_id_from_agent_id(agent_id)
            logger.debug(f"Extracted user_id '{effective_user_id}' for delete from agent_id '{agent_id}' using utility function.")
        except ValueError as e:
            logger.warning(f"Could not extract user_id for delete from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to delete agent ID: {agent_id} for user_id: {effective_user_id}")

    # Delete from in-memory store
    in_memory_deleted = False
    if agent_id in agents_store:
        # Ensure we are deleting an agent that belongs to the user if user_id was part of the agent_id structure
        # This check is implicitly handled if agent_id itself is user-specific or if get_agent was called first.
        # For direct deletion from agents_store, if agents_store is global, this might need more care.
        # Assuming agent_id is globally unique and user_id check is for persistence layer.
        if agents_store[agent_id].get("user_id", DEFAULT_USER_ID) == effective_user_id:
            del agents_store[agent_id]
            in_memory_deleted = True
            logger.info(f"Service: Deleted agent {agent_id} from memory for user_id: {effective_user_id}.")
        else:
            logger.warning(f"Service: Agent {agent_id} found in memory but does not belong to user_id: {effective_user_id}. Not deleted from memory.")


    # Delete from disk if persistence is enabled
    disk_deleted = False
    if ENABLE_PERSISTENCE:
        disk_deleted = persistence_service.delete_persisted_agent(effective_user_id, agent_id) # Use effective_user_id
        if disk_deleted:
            logger.info(f"Service: Deleted agent {agent_id} from disk for user_id: {effective_user_id}.")

    if in_memory_deleted or disk_deleted:
        logger.info(f"Service: Deletion process for agent ID {agent_id} (user_id: {effective_user_id}) completed. In-memory: {in_memory_deleted}, Disk: {disk_deleted}.")
    else:
        logger.warning(f"Service: Agent ID {agent_id} not found for deletion for user_id: {effective_user_id}.")
    return in_memory_deleted or disk_deleted


def get_agent_summary(
    agent_id: str, force_refresh: bool = False, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Get an agent's summary.

    Args:
        agent_id: ID of the agent
        force_refresh: Whether to force a refresh of the summary
        current_time: The current time (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        Dictionary with summary and last_refreshed, or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        try:
            effective_user_id = extract_user_id_from_agent_id(agent_id)
            logger.debug(f"Extracted user_id '{effective_user_id}' for summary from agent_id '{agent_id}' using utility function.")
        except ValueError as e:
            logger.warning(f"Could not extract user_id for summary from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to get summary for agent ID: {agent_id}, user_id: {effective_user_id}, force_refresh: {force_refresh}")

    agent_data = get_agent(agent_id, effective_user_id)
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for summary retrieval (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    summary = agent.get_summary(force_refresh=force_refresh, now=now)
    agent_data["summary"] = summary
    agent_data["last_refreshed"] = agent.last_refreshed

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE and force_refresh: # Only save if summary was refreshed
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id
    logger.info(f"Service: Summary retrieved for agent ID: {agent_id}, user_id: {effective_user_id}.")
    return {
        "summary": summary,
        "last_refreshed": agent.last_refreshed
    }


def generate_reaction(
    agent_id: str, observation: str, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[Tuple[bool, str]]:
    """
    Generate a reaction from an agent based on an observation.

    Args:
        agent_id: ID of the agent
        observation: The observation for the agent to react to
        current_time: The current time (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        Tuple of (is_dialogue, reaction) or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        try:
            effective_user_id = extract_user_id_from_agent_id(agent_id)
            logger.debug(f"Extracted user_id '{effective_user_id}' for reaction from agent_id '{agent_id}' using utility function.")
        except ValueError as e:
            logger.warning(f"Could not extract user_id for reaction from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Generating reaction for agent ID: {agent_id}, user_id: {effective_user_id}, observation: '{observation[:50]}...'")

    agent_data = get_agent(agent_id, effective_user_id)
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for reaction generation (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    reaction = agent.generate_reaction(observation=observation, now=now)

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE: # Agent's memory might have changed
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id
    logger.info(f"Service: Reaction generated for agent ID: {agent_id}, user_id: {effective_user_id}. Result: {reaction}")
    return reaction


def generate_dialogue_response(
    agent_id: str, observation: str, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[Tuple[bool, str]]:
    """
    Generate a dialogue response from an agent based on an observation.

    Args:
        agent_id: ID of the agent
        observation: The observation for the agent to respond to
        current_time: The current time (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        Tuple of (is_dialogue_continuing, response) or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        try:
            effective_user_id = extract_user_id_from_agent_id(agent_id)
            logger.debug(f"Extracted user_id '{effective_user_id}' for dialogue from agent_id '{agent_id}' using utility function.")
        except ValueError as e:
            logger.warning(f"Could not extract user_id for dialogue from agent_id '{agent_id}' using utility: {e}. Falling back to DEFAULT_USER_ID.")
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Generating dialogue response for agent ID: {agent_id}, user_id: {effective_user_id}, observation: '{observation[:50]}...'")

    agent_data = get_agent(agent_id, effective_user_id)
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for dialogue generation (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    response = agent.generate_dialogue_response(observation=observation, now=now)

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE: # Agent's memory might have changed
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id
    logger.info(f"Service: Dialogue response generated for agent ID: {agent_id}, user_id: {effective_user_id}. Result: {response}")
    return response
