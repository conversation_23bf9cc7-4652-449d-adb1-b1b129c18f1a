@echo off
REM Quick Logs Viewer for Development Environment
REM This script shows logs from the running containers

echo.
echo ========================================
echo  Viewing Development Environment Logs
echo ========================================
echo.

REM Check if Dock<PERSON> is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running.
    echo.
    pause
    exit /b 1
)

REM Check if containers are running
docker-compose ps | findstr "Up" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ No containers are currently running.
    echo    Start the environment first with: start-dev.bat
    echo.
    pause
    exit /b 1
)

echo ✅ Containers are running
echo.
echo 📋 Showing live logs (Press Ctrl+C to exit)...
echo.

REM Show logs with follow
docker-compose logs -f api

echo.
echo 📋 Log viewing stopped.
echo.
pause
