import base64
import logging
import os
from typing import List, <PERSON>ct, <PERSON><PERSON>, Optional

from github import Github, GithubException, UnknownObjectException, ContentFile
from fastapi import HTTPException

logger = logging.getLogger(__name__)

# Environment variables for GitHub access
GITHUB_REPO_OWNER = os.getenv("GITHUB_REPO_OWNER")
GITHUB_REPO_NAME = os.getenv("GITHUB_REPO_NAME")
GITHUB_BRANCH = os.getenv("GITHUB_BRANCH", "main")  # Default to 'main' if not set
GITHUB_PAT = os.getenv("GITHUB_PAT")

class GitHubService:
    def __init__(self):
        if not all([GITHUB_REPO_OWNER, GITHUB_REPO_NAME, GITHUB_PAT]):
            logger.error("GitHub environment variables (GITHUB_REPO_OWNER, GITHU<PERSON>_REPO_NAME, GITHUB_PAT) are not fully configured.")
            # This service should not be usable if not configured, but routes will handle raising HTTPException
            self.g = None
            self.repo = None
        else:
            try:
                self.g = Github(GITHUB_PAT)
                self.repo = self.g.get_repo(f"{GITHUB_REPO_OWNER}/{GITHUB_REPO_NAME}")
                logger.info(f"GitHubService initialized for repo: {GITHUB_REPO_OWNER}/{GITHUB_REPO_NAME} on branch {GITHUB_BRANCH}")
            except GithubException as e:
                logger.error(f"Failed to initialize GitHub client or get repo: {e}")
                self.g = None
                self.repo = None

    def _check_configuration_and_raise(self):
        if not self.repo:
            logger.error("GitHubService is not properly configured or failed to initialize.")
            raise HTTPException(status_code=503, detail="GitHub service not configured or unavailable.")

    def list_directory_contents(self, path: str) -> Tuple[List[str], List[str]]:
        self._check_configuration_and_raise()
        logger.info(f"Service (GitHubService): Listing contents for path '{path}' in repo '{GITHUB_REPO_OWNER}/{GITHUB_REPO_NAME}' on branch '{GITHUB_BRANCH}'")
        
        files: List[str] = []
        folders: List[str] = []

        try:
            # Ensure path is clean, remove leading/trailing slashes for consistency with GitHub API
            clean_path = path.strip('/')
            
            contents = self.repo.get_contents(clean_path, ref=GITHUB_BRANCH)
            
            if isinstance(contents, ContentFile.ContentFile): # Path was a file, not a directory
                logger.warning(f"Path '{path}' resolved to a file, not a directory. Returning empty lists.")
                return files, folders

            for content_file in contents:
                if content_file.type == "dir":
                    folders.append(content_file.name)
                elif content_file.type == "file" and content_file.name.endswith(".json"): # Only list .json files as per plugin expectation
                    files.append(content_file.name)
            
            logger.info(f"Service (GitHubService): Found {len(files)} files and {len(folders)} folders in '{path}'")
            return files, folders
        except UnknownObjectException:
            logger.warning(f"Service (GitHubService): Path '{path}' not found in repository.")
            # For list, not finding the path could mean it's an empty directory or non-existent.
            # The plugin seems to expect an empty list rather than a 404 in this case for listing.
            return files, folders 
        except GithubException as e:
            logger.error(f"Service (GitHubService): GitHub API error while listing contents for path '{path}': {e}")
            raise HTTPException(status_code=500, detail=f"GitHub API error: {e.status} - {e.data.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Service (GitHubService): Unexpected error listing contents for path '{path}': {e}")
            raise HTTPException(status_code=500, detail="Internal server error while listing repository contents.")

    def get_file_content(self, file_path: str) -> str:
        self._check_configuration_and_raise()
        logger.info(f"Service (GitHubService): Getting content for file '{file_path}' in repo '{GITHUB_REPO_OWNER}/{GITHUB_REPO_NAME}' on branch '{GITHUB_BRANCH}'")
        
        # Ensure path is clean, remove leading/trailing slashes
        clean_file_path = file_path.strip('/')

        try:
            file_content = self.repo.get_contents(clean_file_path, ref=GITHUB_BRANCH)
            
            if isinstance(file_content, list): # Path was a directory, not a file
                 logger.error(f"Service (GitHubService): Path '{file_path}' is a directory, not a file.")
                 raise HTTPException(status_code=400, detail=f"Path '{file_path}' is a directory, not a file.")

            if file_content.encoding == "base64":
                decoded_content = base64.b64decode(file_content.content).decode('utf-8')
                logger.info(f"Service (GitHubService): Successfully fetched and decoded content for '{file_path}'.")
                return decoded_content
            else:
                # Should not happen for text files like JSON, but handle defensively
                logger.warning(f"Service (GitHubService): File '{file_path}' has unexpected encoding '{file_content.encoding}'. Attempting to return raw content.")
                return file_content.decoded_content.decode('utf-8')

        except UnknownObjectException:
            logger.error(f"Service (GitHubService): File '{file_path}' not found in repository.")
            raise HTTPException(status_code=404, detail=f"File '{file_path}' not found in repository.")
        except GithubException as e:
            logger.error(f"Service (GitHubService): GitHub API error while getting content for file '{file_path}': {e}")
            raise HTTPException(status_code=500, detail=f"GitHub API error: {e.status} - {e.data.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Service (GitHubService): Unexpected error getting content for file '{file_path}': {e}")
            raise HTTPException(status_code=500, detail="Internal server error while getting file content.")

# Singleton instance
github_service = GitHubService()
