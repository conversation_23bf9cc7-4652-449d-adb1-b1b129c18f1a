// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "WorldEngineModels.generated.h"

/**
 * Result of a single agent's turn in the world simulation
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FAgentTurnResult
{
    GENERATED_BODY()

    /** What the agent observed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString Observation;

    /** The agent's reaction/action */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString Action;

    /** Whether the action succeeded */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    bool bSuccess;

    /** Action feasibility score (0-10) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    float FeasibilityScore;

    /** Reason for action failure */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString FailureReason;

    /** Agent's updated status */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString UpdatedStatus;

    /** Error message if turn processing failed */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString Error;

    FAgentTurnResult()
    {
        bSuccess = false;
        FeasibilityScore = 0.0f;
    }
};

/**
 * Request to create a new world engine
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FWorldEngineCreateRequest
{
    GENERATED_BODY()

    /** Unique identifier for the world */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString WorldId;

    /** Initial world rules */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    TArray<FString> Rules;

    /** Enable verbose logging */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    bool bVerbose;

    FWorldEngineCreateRequest()
    {
        bVerbose = false;
    }
};

/**
 * Current state of a world engine
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FWorldEngineState
{
    GENERATED_BODY()

    /** World identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString WorldId;

    /** Current turn number */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    int32 TurnCount;

    /** Number of world rules */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    int32 RuleCount;

    FWorldEngineState()
    {
        TurnCount = 0;
        RuleCount = 0;
    }
};

/**
 * Request to execute a turn in the world simulation
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FTurnExecutionRequest
{
    GENERATED_BODY()

    /** List of agent IDs to include in the turn */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    TArray<FString> AgentIds;
};

/**
 * Response from turn execution
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FTurnExecutionResponse
{
    GENERATED_BODY()

    /** Turn number */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    int32 Turn;

    /** World identifier */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString WorldId;

    /** ISO timestamp of turn execution */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    FString Timestamp;

    /** Results for each agent (stored as parallel arrays since TMap isn't Blueprint-friendly) */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    TArray<FString> AgentIds;

    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    TArray<FAgentTurnResult> AgentResults;

    FTurnExecutionResponse()
    {
        Turn = 0;
    }
};

/**
 * Request to update world rules
 */
USTRUCT(BlueprintType)
struct AGENTBRIDGE_API FWorldRuleUpdateRequest
{
    GENERATED_BODY()

    /** New rules to add */
    UPROPERTY(BlueprintReadWrite, Category = "AgentBridge|WorldEngine")
    TArray<FString> Rules;
};
