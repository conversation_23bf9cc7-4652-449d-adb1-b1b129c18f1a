// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Models/AgentModels.h" // Added for FAgentCreateRequest
#include "AgentCommunicationComponent.generated.h"

class UWebServiceSubsystem;
class UBaseApiRequest;
// Forward declare FAgentCreateRequest if not including the full header,
// but including is cleaner if it's a direct parameter type.
// struct FAgentCreateRequest;

/**
 * Delegate for agent creation completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAgentCreated, const FString&, BackendAgentId);

/**
 * Delegate for agent creation failure
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAgentCreationFailed, const FString&, ErrorResponse);

/**
 * Delegate for agent operation completion
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAgentOperationCompleted, bool, bSuccess, const FString&, Response);

/**
 * Component that handles communication with a specific agent on the backend
 */
UCLASS(ClassGroup=(AgentBridge), meta=(BlueprintSpawnableComponent))
class AGENTBRIDGE_API UAgentCommunicationComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UAgentCommunicationComponent();

    virtual void BeginPlay() override;

    /**
     * Create a new agent on the backend
     * @param AgentCreateData - The data required to create the agent
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Agent", meta=(DisplayName = "Create Backend Agent (Advanced)"))
    void CreateBackendAgent(const FAgentCreateRequest& AgentCreateData);

    /**
     * Add a memory to the agent
     * @param MemoryCreateData - The data required to create the memory
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Agent", meta=(DisplayName = "Add Memory To Agent (Advanced)"))
    void AddMemoryToAgent(const FMemoryCreateRequest& MemoryCreateData);

    /**
     * Generate a reaction from the agent
     * @param ReactionRequestData - The data for the reaction request (observation, current time)
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Agent", meta=(DisplayName = "Generate Agent Reaction (Advanced)"))
    void GenerateAgentReaction(const FAgentReactionRequest& ReactionRequestData);

    /**
     * Generate a dialogue response from the agent
     * @param DialogueRequestData - The data for the dialogue request (observation, current time), uses FAgentReactionRequest struct
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Agent", meta=(DisplayName = "Generate Dialogue Response (Advanced)"))
    void GenerateDialogueResponse(const FAgentReactionRequest& DialogueRequestData);

    /**
     * Get the backend agent ID
     * @return The backend agent ID
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Agent")
    FString GetBackendAgentId() const;

    /**
     * Set the backend agent ID directly (for loading existing agents)
     * @param NewBackendAgentId - The new backend agent ID
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Agent")
    void SetBackendAgentId(const FString& NewBackendAgentId);

    /**
     * Check if the component has a valid backend agent ID
     * @return True if the component has a valid backend agent ID, false otherwise
     */
    UFUNCTION(BlueprintPure, Category = "AgentBridge|Agent")
    bool HasValidBackendAgentId() const;

    /**
     * Event called when an agent is created successfully
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Agent")
    FOnAgentCreated OnBackendAgentCreatedSuccessfully;

    /**
     * Event called when agent creation fails
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Agent")
    FOnAgentCreationFailed OnBackendAgentCreationFailed;

    /**
     * Event called when a memory is added
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Agent")
    FOnAgentOperationCompleted OnMemoryAddedToAgent;

    /**
     * Event called when a reaction is generated
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Agent")
    FOnAgentOperationCompleted OnReactionGenerated;

    /**
     * Event called when a dialogue response is generated
     */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Agent")
    FOnAgentOperationCompleted OnDialogueResponseGenerated;

private:
    // The backend agent ID
    UPROPERTY(VisibleInstanceOnly, Category = "AgentBridge")
    FString BackendAgentId;

    // The web service subsystem
    UPROPERTY()
    UWebServiceSubsystem* WebServiceSubsystem;

    // Callback for agent creation
    UFUNCTION()
    void OnCreateAgentSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response);

    // Callback for memory addition
    UFUNCTION()
    void OnAddMemorySuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response);

    // Callback for reaction generation
    UFUNCTION()
    void OnGenerateReactionSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response);

    // Callback for dialogue response generation
    UFUNCTION()
    void OnGenerateDialogueSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response);
};
