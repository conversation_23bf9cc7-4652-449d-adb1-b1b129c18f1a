"""
Timeout and request monitoring middleware for FastAPI.

This middleware helps prevent worker timeouts by:
1. Monitoring request processing times
2. Providing early warnings for long-running requests
3. Adding timeout handling for requests
4. Logging request performance metrics
"""
import asyncio
import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class TimeoutMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle request timeouts and monitor request processing times.
    """
    
    def __init__(
        self,
        app,
        timeout_seconds: int = 240,  # 4 minutes (less than gunicorn timeout)
        warning_threshold: int = 60,  # Warn after 1 minute
        log_slow_requests: bool = True
    ):
        super().__init__(app)
        self.timeout_seconds = timeout_seconds
        self.warning_threshold = warning_threshold
        self.log_slow_requests = log_slow_requests
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process the request with timeout handling and performance monitoring.
        """
        start_time = time.time()
        request_id = id(request)
        
        # Log request start
        logger.info(
            f"Request started: {request.method} {request.url.path} "
            f"(ID: {request_id})"
        )
        
        try:
            # Create a task for the request processing
            response_task = asyncio.create_task(call_next(request))
            
            # Create a timeout task
            timeout_task = asyncio.create_task(
                asyncio.sleep(self.timeout_seconds)
            )
            
            # Create a warning task
            warning_task = asyncio.create_task(
                asyncio.sleep(self.warning_threshold)
            )
            
            # Wait for the first task to complete
            done, pending = await asyncio.wait(
                [response_task, timeout_task, warning_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Check which task completed first
            completed_task = done.pop()
            
            if completed_task == response_task:
                # Request completed successfully
                response = completed_task.result()
                processing_time = time.time() - start_time
                
                # Log completion
                logger.info(
                    f"Request completed: {request.method} {request.url.path} "
                    f"(ID: {request_id}) in {processing_time:.2f}s"
                )
                
                # Log slow requests
                if self.log_slow_requests and processing_time > self.warning_threshold:
                    logger.warning(
                        f"Slow request detected: {request.method} {request.url.path} "
                        f"took {processing_time:.2f}s"
                    )
                
                # Add timing headers
                response.headers["X-Process-Time"] = str(processing_time)
                return response
                
            elif completed_task == warning_task:
                # Warning threshold reached, but continue processing
                logger.warning(
                    f"Request taking longer than expected: {request.method} {request.url.path} "
                    f"(ID: {request_id}) - {self.warning_threshold}s elapsed"
                )
                
                # Wait for the actual response or timeout
                done, pending = await asyncio.wait(
                    [response_task, timeout_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                completed_task = done.pop()
                
                if completed_task == response_task:
                    response = completed_task.result()
                    processing_time = time.time() - start_time
                    
                    logger.info(
                        f"Long request completed: {request.method} {request.url.path} "
                        f"(ID: {request_id}) in {processing_time:.2f}s"
                    )
                    
                    response.headers["X-Process-Time"] = str(processing_time)
                    return response
                else:
                    # Timeout occurred
                    processing_time = time.time() - start_time
                    logger.error(
                        f"Request timeout: {request.method} {request.url.path} "
                        f"(ID: {request_id}) after {processing_time:.2f}s"
                    )
                    
                    return JSONResponse(
                        status_code=504,
                        content={
                            "detail": "Request timeout",
                            "message": f"Request took longer than {self.timeout_seconds} seconds",
                            "processing_time": processing_time
                        }
                    )
            
            else:
                # Timeout occurred immediately
                processing_time = time.time() - start_time
                logger.error(
                    f"Request timeout: {request.method} {request.url.path} "
                    f"(ID: {request_id}) after {processing_time:.2f}s"
                )
                
                return JSONResponse(
                    status_code=504,
                    content={
                        "detail": "Request timeout",
                        "message": f"Request took longer than {self.timeout_seconds} seconds",
                        "processing_time": processing_time
                    }
                )
        
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                f"Request error: {request.method} {request.url.path} "
                f"(ID: {request_id}) after {processing_time:.2f}s - {str(e)}",
                exc_info=True
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Internal server error",
                    "message": str(e),
                    "processing_time": processing_time
                }
            )


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log detailed request information for debugging.
    """
    
    def __init__(self, app, log_body: bool = False):
        super().__init__(app)
        self.log_body = log_body
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Log request details and response information.
        """
        start_time = time.time()
        
        # Log request details
        logger.debug(
            f"Incoming request: {request.method} {request.url} "
            f"Headers: {dict(request.headers)}"
        )
        
        if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    logger.debug(f"Request body: {body.decode('utf-8')[:1000]}...")
            except Exception as e:
                logger.debug(f"Could not read request body: {e}")
        
        # Process request
        response = await call_next(request)
        
        # Log response details
        processing_time = time.time() - start_time
        logger.debug(
            f"Response: {response.status_code} for {request.method} {request.url.path} "
            f"in {processing_time:.3f}s"
        )
        
        return response
