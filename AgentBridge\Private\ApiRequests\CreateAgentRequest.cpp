// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/CreateAgentRequest.h"
#include "CoreMinimal.h"
#include "AgentBridge.h"
#include "Json.h"
#include "JsonUtilities.h"

UCreateAgentRequest::UCreateAgentRequest()
{
}

void UCreateAgentRequest::SetAgentData(const FAgentCreateRequest& InAgentData)
{
    AgentData = InAgentData;
}

FString UCreateAgentRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UCreateAgentRequest::GetEndpoint() const
{
    return TEXT("/api/agents/");
}

TSharedPtr<FJsonObject> UCreateAgentRequest::GetRequestDataAsJson() const
{
    // Create the request body as a JSON object
    TSharedPtr<FJsonObject> RequestObj = MakeShareable(new FJsonObject);
    RequestObj->SetStringField(TEXT("name"), AgentData.Name);
    RequestObj->SetStringField(TEXT("traits"), AgentData.Traits);
    RequestObj->SetStringField(TEXT("status"), AgentData.Status);

    if (AgentData.Age > 0)
    {
        RequestObj->SetNumberField(TEXT("age"), AgentData.Age);
    }

    // if (!AgentData.LlmModel.IsEmpty()) // Removed LlmModel serialization
    // {
    //     RequestObj->SetStringField(TEXT("llm_model"), AgentData.LlmModel);
    // }

    if (AgentData.ReflectionThreshold > 0.0f)
    {
        RequestObj->SetNumberField(TEXT("reflection_threshold"), AgentData.ReflectionThreshold);
    }

    if (AgentData.ImportanceWeight > 0.0f)
    {
        RequestObj->SetNumberField(TEXT("importance_weight"), AgentData.ImportanceWeight);
    }

    RequestObj->SetBoolField(TEXT("verbose"), AgentData.bVerbose);

    if (AgentData.InitialMemories.Num() > 0)
    {
        TArray<TSharedPtr<FJsonValue>> MemoryArray;
        for (const FString& Memory : AgentData.InitialMemories)
        {
            MemoryArray.Add(MakeShareable(new FJsonValueString(Memory)));
        }
        RequestObj->SetArrayField(TEXT("initial_memories"), MemoryArray);
    }

    return RequestObj;
}

FString UCreateAgentRequest::GetRequestBody() const
{
    // Get the JSON object and serialize it to a string
    TSharedPtr<FJsonObject> RequestObj = GetRequestDataAsJson();

    FString RequestBody;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestBody);
    FJsonSerializer::Serialize(RequestObj.ToSharedRef(), Writer);

    return RequestBody;
}

void UCreateAgentRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    // Log the response for debugging
    if (!bWasSuccessful || StatusCode != 200)
    {
        // Log detailed error information
        UE_LOG(LogAgentBridge, Error, TEXT("CreateAgentRequest failed: Status=%d, Response=%s"), StatusCode, *Response);

        // Try to parse error details if available
        TSharedPtr<FJsonObject> ErrorObject;
        TSharedRef<TJsonReader<>> ErrorReader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(ErrorReader, ErrorObject) && ErrorObject.IsValid())
        {
            // Extract error details if available
            if (ErrorObject->HasField(TEXT("detail")))
            {
                FString ErrorDetail = ErrorObject->GetStringField(TEXT("detail"));
                UE_LOG(LogAgentBridge, Error, TEXT("Error detail: %s"), *ErrorDetail);
            }

            if (ErrorObject->HasField(TEXT("message")))
            {
                FString ErrorMessage = ErrorObject->GetStringField(TEXT("message"));
                UE_LOG(LogAgentBridge, Error, TEXT("Error message: %s"), *ErrorMessage);
            }

            // Log the request data that was sent
            TSharedPtr<FJsonObject> RequestData = GetRequestDataAsJson();
            FString RequestDataStr;
            TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestDataStr);
            FJsonSerializer::Serialize(RequestData.ToSharedRef(), Writer);
            UE_LOG(LogAgentBridge, Error, TEXT("Request data that caused the error: %s"), *RequestDataStr);
        }

        return;
    }

    // Process successful response
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        // Extract agent data from response
        CreatedAgentData.Id = JsonObject->GetStringField(TEXT("id"));
        CreatedAgentData.Name = JsonObject->GetStringField(TEXT("name"));

        // Safely parse 'age'
        if (JsonObject->HasField(TEXT("age")))
        {
            const TSharedPtr<FJsonValue> AgeJsonValue = JsonObject->TryGetField(TEXT("age"));
            if (AgeJsonValue.IsValid() && !AgeJsonValue->IsNull())
            {
                // Attempt to get the number only if the field is present, valid, and not null
                double AgeDouble;
                if (AgeJsonValue->TryGetNumber(AgeDouble))
                {
                    CreatedAgentData.Age = static_cast<int32>(AgeDouble);
                }
                else
                {
                    // The field is present and not null, but it's not a number (e.g., "age": "some_string")
                    // Log a warning or handle as an error if appropriate. For now, default to 0.
                    UE_LOG(LogAgentBridge, Warning, TEXT("CreateAgentRequest: 'age' field received non-numeric value. Defaulting to 0."));
                    CreatedAgentData.Age = 0;
                }
            }
            else // Field is present but null, or AgeJsonValue is somehow invalid
            {
                CreatedAgentData.Age = 0; // Default value if 'age' is null
            }
        }
        else // Field 'age' is not present in the JSON response
        {
            CreatedAgentData.Age = 0; // Default value if 'age' field is missing
        }

        CreatedAgentData.Traits = JsonObject->GetStringField(TEXT("traits"));
        CreatedAgentData.Status = JsonObject->GetStringField(TEXT("status"));
        CreatedAgentData.Summary = JsonObject->GetStringField(TEXT("summary"));
        CreatedAgentData.LastRefreshed = JsonObject->GetStringField(TEXT("last_refreshed"));

        UE_LOG(LogAgentBridge, Log, TEXT("Successfully processed agent creation response: ID=%s, Name=%s"),
            *CreatedAgentData.Id, *CreatedAgentData.Name);
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse successful response as JSON: %s"), *Response);
    }
}

FAgentData UCreateAgentRequest::GetCreatedAgentData() const
{
    return CreatedAgentData;
}

FString UCreateAgentRequest::GetRequestDataAsString() const
{
    TSharedPtr<FJsonObject> RequestData = GetRequestDataAsJson();
    FString RequestDataStr;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestDataStr);
    FJsonSerializer::Serialize(RequestData.ToSharedRef(), Writer);
    return RequestDataStr;
}
