"""
Service layer for World Engine operations.

This module provides high-level functions for managing world engines,
executing turns, and handling world rules.
"""

import logging
from typing import Dict, List, Optional, Any

from core.world_engine import WorldEngine
from services.agent_service import get_agent
from utils.llm_utils import get_llm
from config import DEFAULT_USER_ID, ENABLE_PERSISTENCE
from services import persistence_service

logger = logging.getLogger(__name__)

# In-memory storage for world engines
world_engines_store: Dict[str, WorldEngine] = {}


def create_world_engine(
    world_id: str,
    rules: Optional[List[str]] = None,
    verbose: bool = False,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new world engine instance.
    
    Args:
        world_id: Unique identifier for the world
        rules: Initial world rules
        verbose: Enable verbose logging
        user_id: User who owns this world
        
    Returns:
        Dictionary containing world engine details
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Creating world engine '{world_id}' for user {user_id}")
    
    # Check if world already exists
    if world_id in world_engines_store:
        logger.warning(f"World engine '{world_id}' already exists")
        return get_world_engine_info(world_id, user_id)
    
    # Create the world engine
    llm = get_llm()
    world_engine = WorldEngine(
        world_id=world_id,
        llm=llm,
        verbose=verbose
    )
    
    # Add initial rules if provided
    if rules:
        world_engine.add_world_rules(rules)
        logger.info(f"Added {len(rules)} initial rules to world '{world_id}'")
    
    # Store in memory
    world_engines_store[world_id] = world_engine
    
    # Prepare return data
    world_data = {
        "world_id": world_id,
        "user_id": user_id,
        "turn_count": world_engine.turn_count,
        "rule_count": len(rules) if rules else 0,
        "verbose": verbose
    }
    
    # Persist if enabled
    if ENABLE_PERSISTENCE:
        persist_world_engine(world_id, user_id)
    
    logger.info(f"Successfully created world engine '{world_id}'")
    return world_data


def get_world_engine(world_id: str, user_id: Optional[str] = None) -> Optional[WorldEngine]:
    """
    Get a world engine instance.
    
    Args:
        world_id: World identifier
        user_id: User ID (for future multi-user support)
        
    Returns:
        WorldEngine instance or None if not found
    """
    # Check in-memory store
    world_engine = world_engines_store.get(world_id)
    if world_engine:
        return world_engine
    
    # TODO: Load from persistence if not in memory
    # For now, return None
    logger.warning(f"World engine '{world_id}' not found")
    return None


def get_world_engine_info(world_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get information about a world engine.
    
    Args:
        world_id: World identifier
        user_id: User ID
        
    Returns:
        Dictionary with world engine info or None
    """
    world_engine = get_world_engine(world_id, user_id)
    if not world_engine:
        return None
    
    # Get rule count (approximate since we can't easily count vector store docs)
    # In a production system, we'd track this separately
    rule_count = 0
    try:
        # Try to get some rules to estimate count
        sample_rules = world_engine.get_relevant_world_rules("", k=100)
        rule_count = len(sample_rules)
    except:
        pass
    
    return {
        "world_id": world_id,
        "turn_count": world_engine.turn_count,
        "rule_count": rule_count,
        "world_state": world_engine.world_state
    }


def update_world_rules(
    world_id: str,
    new_rules: List[str],
    user_id: Optional[str] = None
) -> bool:
    """
    Add new rules to a world engine.
    
    Args:
        world_id: World identifier
        new_rules: New rules to add
        user_id: User ID
        
    Returns:
        True if successful
    """
    world_engine = get_world_engine(world_id, user_id)
    if not world_engine:
        logger.error(f"World engine '{world_id}' not found")
        return False
    
    world_engine.add_world_rules(new_rules)
    logger.info(f"Added {len(new_rules)} rules to world '{world_id}'")
    
    # Persist if enabled
    if ENABLE_PERSISTENCE:
        persist_world_engine(world_id, user_id or DEFAULT_USER_ID)
    
    return True


def execute_world_turn(
    world_id: str,
    agent_ids: List[str],
    user_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Execute a turn in the world simulation.
    
    Args:
        world_id: World identifier
        agent_ids: List of agent IDs to include in the turn
        user_id: User ID
        
    Returns:
        Turn execution results or None if error
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Executing turn for world '{world_id}' with {len(agent_ids)} agents")
    
    # Get world engine
    world_engine = get_world_engine(world_id, user_id)
    if not world_engine:
        logger.error(f"World engine '{world_id}' not found")
        return None
    
    # Get agent data for all agents
    agents_data = []
    for agent_id in agent_ids:
        agent_data = get_agent(agent_id, user_id)
        if agent_data:
            agents_data.append(agent_data)
        else:
            logger.warning(f"Agent '{agent_id}' not found, skipping")
    
    if not agents_data:
        logger.error("No valid agents found for turn execution")
        return None
    
    # Execute the turn
    try:
        turn_results = world_engine.execute_turn(agents_data)
        
        # Persist agent updates if enabled
        if ENABLE_PERSISTENCE:
            for agent_data in agents_data:
                persistence_service.save_agent(agent_data, user_id)
        
        logger.info(f"Turn {turn_results['turn']} completed for world '{world_id}'")
        return turn_results
        
    except Exception as e:
        logger.error(f"Error executing turn for world '{world_id}': {e}", exc_info=True)
        return None


def persist_world_engine(world_id: str, user_id: str) -> bool:
    """
    Persist world engine state (placeholder for future implementation).
    
    Args:
        world_id: World identifier
        user_id: User ID
        
    Returns:
        True if successful
    """
    # TODO: Implement world engine persistence
    # This would save world rules, state, turn count, etc.
    logger.debug(f"World engine persistence not yet implemented for '{world_id}'")
    return True


def list_world_engines(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    List all world engines for a user.
    
    Args:
        user_id: User ID
        
    Returns:
        List of world engine info dictionaries
    """
    user_id = user_id or DEFAULT_USER_ID
    logger.info(f"Listing world engines for user {user_id}")
    
    # For now, return all in-memory world engines
    # In a multi-user system, we'd filter by user_id
    world_engines_list = []
    for world_id, world_engine in world_engines_store.items():
        info = get_world_engine_info(world_id, user_id)
        if info:
            world_engines_list.append(info)
    
    return world_engines_list


def delete_world_engine(world_id: str, user_id: Optional[str] = None) -> bool:
    """
    Delete a world engine.
    
    Args:
        world_id: World identifier
        user_id: User ID
        
    Returns:
        True if deleted successfully
    """
    if world_id not in world_engines_store:
        logger.warning(f"World engine '{world_id}' not found for deletion")
        return False
    
    del world_engines_store[world_id]
    logger.info(f"Deleted world engine '{world_id}'")
    
    # TODO: Delete from persistence
    
    return True
