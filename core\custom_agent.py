"""Custom Agent classes for the project, inheriting from local base agent code."""

from typing import Optional # Added for type hinting 'age' and 'reflection_threshold'
from langchain_core.language_models import BaseLanguageModel # For type hinting
from langchain.retrievers import TimeWeightedVectorStoreRetriever # For type hinting

from .generative_agents.generative_agent import GenerativeAgent as BaseGenerativeAgent
from .generative_agents.memory import GenerativeAgentMemory as BaseGenerativeAgentMemory

class CustomGenerativeAgentMemory(BaseGenerativeAgentMemory):
    """
    Custom generative agent memory for our project.
    Inherits from the local copy of Langchain's GenerativeAgentMemory.
    This class serves as a minimal override for now, allowing for future extensions.
    """
    def __init__(
        self,
        llm: BaseLanguageModel,
        memory_retriever: TimeWeightedVectorStoreRetriever,
        verbose: bool = False,
        reflection_threshold: Optional[float] = None,
        importance_weight: float = 0.15,
        # Add any other parameters from the base class __init__ if needed
        **kwargs 
    ):
        super().__init__(
            llm=llm,
            memory_retriever=memory_retriever,
            verbose=verbose,
            reflection_threshold=reflection_threshold,
            importance_weight=importance_weight,
            **kwargs
        )
        # Project-specific memory initializations can go here later

class CustomGenerativeAgent(BaseGenerativeAgent):
    """
    Custom generative agent for our project.
    Inherits from the local copy of Langchain's GenerativeAgent.
    This class serves as a minimal override for now, allowing for future extensions.
    """
    def __init__(
        self,
        name: str,
        status: str,
        memory: CustomGenerativeAgentMemory, # Use our custom memory
        llm: BaseLanguageModel,
        age: Optional[int] = None,
        traits: str = "N/A",
        verbose: bool = False,
        # Add any other parameters from the base class __init__ if needed
        **kwargs
    ):
        super().__init__(
            name=name,
            age=age,
            traits=traits,
            status=status,
            memory=memory, # Pass the custom memory instance
            llm=llm,
            verbose=verbose,
            **kwargs
        )
        # Project-specific agent initializations can go here later
