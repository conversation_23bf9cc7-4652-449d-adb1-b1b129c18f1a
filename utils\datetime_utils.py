"""
Utility functions for working with datetime objects.
"""
import logging
from datetime import datetime
from contextlib import contextmanager
from typing import Iterator, Optional, Callable

logger = logging.getLogger(__name__)

# Create a module-level variable to store the current mock datetime
_CURRENT_MOCK_DATETIME = None

@contextmanager
def serializable_mock_now(dt_value: Optional[datetime] = None) -> Iterator[Callable[[], datetime]]:
    """
    Context manager for providing a mock datetime.now function.
    This is a replacement for langchain.utils.mock_now that can be pickled.

    Args:
        dt_value: The datetime value to use for the mock. If None, uses the real datetime.now()

    Yields:
        A function that returns the mock datetime or the real datetime.now()
    """
    global _CURRENT_MOCK_DATETIME

    if dt_value is None:
        # If no datetime is provided, just use the real datetime.now
        yield datetime.now
        return

    # Store the previous mock datetime (if any)
    previous_mock_datetime = _CURRENT_MOCK_DATETIME

    try:
        # Set the mock datetime
        _CURRENT_MOCK_DATETIME = dt_value

        # Return a function that will return our mock datetime
        def mock_now_func() -> datetime:
            return dt_value

        yield mock_now_func
    finally:
        # Restore the previous mock datetime (if any)
        _CURRENT_MOCK_DATETIME = previous_mock_datetime
