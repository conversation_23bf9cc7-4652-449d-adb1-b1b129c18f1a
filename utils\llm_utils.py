import os
from typing import Optional, Dict, Any, List

from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.prompts import PromptTemplate
from langchain_openai import Chat<PERSON>penAI, OpenAIEmbeddings
from langchain_core.language_models import BaseLanguageModel
from langchain_core.embeddings import Embeddings
from openai import OpenAI

from config import OPENAI_API_KEY, DEFAULT_MODEL_NAME, DEFAULT_EMBEDDINGS_MODEL

def get_llm(
    model_name: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: Optional[int] = None,
    response_format: Optional[Dict[str, str]] = None,
) -> BaseLanguageModel:
    """
    Get a language model instance.

    Args:
        model_name: Name of the model to use (defaults to config value)
        temperature: Temperature for generation (0.0 to 1.0)
        max_tokens: Maximum tokens to generate
        response_format: Optional format specification for the response (e.g., {"type": "json_object"})

    Returns:
        An instance of a language model
    """
    # the newest OpenAI model is "gpt-4o" which was released May 13, 2024.
    # do not change this unless explicitly requested by the user
    model = model_name or DEFAULT_MODEL_NAME

    # Validate API key
    if not OPENAI_API_KEY:
        import logging
        logging.getLogger(__name__).error("OPENAI_API_KEY is not set. LLM operations will fail.")

    # Create and return the language model with explicit API key
    try:
        # Configure the model with the provided parameters
        model_kwargs = {}
        if response_format:
            model_kwargs["response_format"] = response_format

        return ChatOpenAI(
            model=model,
            temperature=temperature,
            openai_api_key=OPENAI_API_KEY,
            max_tokens=max_tokens,
            model_kwargs=model_kwargs
        )
    except Exception as e:
        import logging
        logging.getLogger(__name__).error(f"Error initializing ChatOpenAI: {e}", exc_info=True)
        # Return a basic model that will at least not crash but log errors
        from langchain_core.language_models.fake import FakeListLLM
        return FakeListLLM(responses=["Error: Could not initialize OpenAI model. Check API key and configuration."])

def get_embeddings(model_name: Optional[str] = None) -> Embeddings:
    """
    Get an embedding model instance.

    Args:
        model_name: Name of the embeddings model to use (defaults to config value)

    Returns:
        An instance of OpenAI embeddings
    """
    model = model_name or DEFAULT_EMBEDDINGS_MODEL

    # Validate API key
    if not OPENAI_API_KEY:
        import logging
        logging.getLogger(__name__).error("OPENAI_API_KEY is not set. Embedding operations will fail.")

    # Create and return the embeddings model with explicit API key
    try:
        return OpenAIEmbeddings(model=model, openai_api_key=OPENAI_API_KEY)
    except Exception as e:
        import logging
        logging.getLogger(__name__).error(f"Error initializing OpenAIEmbeddings: {e}", exc_info=True)
        # Return a basic embeddings model that will at least not crash
        from langchain_core.embeddings import FakeEmbeddings
        return FakeEmbeddings(size=1536)  # OpenAI embeddings are typically 1536 dimensions

def create_chain(
    llm: BaseLanguageModel,
    prompt_template: str,
    verbose: bool = False,
    memory: Optional[Any] = None,
) -> LLMChain:
    """
    Create a language model chain with the specified prompt.

    Args:
        llm: Language model to use
        prompt_template: Template string for the prompt
        verbose: Whether to enable verbose output
        memory: Optional memory object to use with the chain

    Returns:
        An LLMChain instance
    """
    prompt = PromptTemplate.from_template(prompt_template)

    return LLMChain(
        llm=llm,
        prompt=prompt,
        verbose=verbose,
        memory=memory
    )

def validate_api_key() -> bool:
    """
    Validate that the API key is set.

    Returns:
        True if the API key is set, False otherwise
    """
    return OPENAI_API_KEY is not None and len(OPENAI_API_KEY) > 0
