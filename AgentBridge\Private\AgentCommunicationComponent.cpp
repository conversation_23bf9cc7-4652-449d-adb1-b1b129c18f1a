// Copyright Epic Games, Inc. All Rights Reserved.

#include "AgentCommunicationComponent.h"
#include "WebServiceSubsystem.h"
#include "ApiRequests/CreateAgentRequest.h"
#include "ApiRequests/AddMemoryRequest.h"
#include "ApiRequests/GenerateReactionRequest.h"
#include "ApiRequests/GenerateDialogueRequest.h" // Added for UGenerateDialogueRequest
#include "Kismet/GameplayStatics.h"
#include "Json.h"
#include "JsonUtilities.h"
#include "Engine/GameInstance.h"
#include "HttpModule.h"
#include "AgentBridge.h"

UAgentCommunicationComponent::UAgentCommunicationComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
}

void UAgentCommunicationComponent::BeginPlay()
{
    Super::BeginPlay();

    // Get the web service subsystem
    UGameInstance* GameInstance = UGameplayStatics::GetGameInstance(GetWorld());
    if (GameInstance)
    {
        WebServiceSubsystem = GameInstance->GetSubsystem<UWebServiceSubsystem>();
        if (WebServiceSubsystem)
        {
            UE_LOG(LogAgentBridge, Log, TEXT("AgentCommunicationComponent initialized for %s"),
                *GetOwner()->GetName());
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to get WebServiceSubsystem in AgentCommunicationComponent for %s"),
                *GetOwner()->GetName());
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to get GameInstance in AgentCommunicationComponent"));
    }
}

void UAgentCommunicationComponent::CreateBackendAgent(const FAgentCreateRequest& AgentCreateData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("UAgentCommunicationComponent::CreateBackendAgent: WebServiceSubsystem is null"));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Creating backend agent with data: Name=%s, Traits=%s, Status=%s, Age=%d, InitialMemoriesCount=%d"),
        *AgentCreateData.Name, *AgentCreateData.Traits, *AgentCreateData.Status, AgentCreateData.Age, AgentCreateData.InitialMemories.Num());

    UCreateAgentRequest* CreateAgentRequest = NewObject<UCreateAgentRequest>();
    CreateAgentRequest->SetAgentData(AgentCreateData);

    // Log the full request data for debugging
    UE_LOG(LogAgentBridge, Log, TEXT("Agent creation request data: %s"), *CreateAgentRequest->GetRequestDataAsString());

    // Bind the completion callback
    CreateAgentRequest->OnRequestCompletedDynamic.AddUniqueDynamic(this, &UAgentCommunicationComponent::OnCreateAgentSuccess_Internal);

    // Execute the request via WebServiceSubsystem
    WebServiceSubsystem->ExecuteApiRequest(CreateAgentRequest);
}

void UAgentCommunicationComponent::AddMemoryToAgent(const FMemoryCreateRequest& MemoryCreateData)
{
    if (MemoryCreateData.AgentId.IsEmpty() && BackendAgentId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("UAgentCommunicationComponent: Cannot add memory, AgentId is not set in MemoryCreateData and BackendAgentId is not set. Agent might not be created or identified."));
        return;
    }

    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("UAgentCommunicationComponent::AddMemoryToAgent: WebServiceSubsystem is null"));
        return;
    }

    // Prioritize AgentId from MemoryCreateData if provided, otherwise use component's BackendAgentId
    FString TargetAgentId = MemoryCreateData.AgentId.IsEmpty() ? BackendAgentId : MemoryCreateData.AgentId;

    if (TargetAgentId.IsEmpty())
    {
         UE_LOG(LogAgentBridge, Warning, TEXT("UAgentCommunicationComponent: Cannot add memory, effective AgentId is empty."));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Adding memory to agent %s: Importance=%.2f, Text=%s"),
        *TargetAgentId, MemoryCreateData.Importance, *MemoryCreateData.MemoryContent);

    UAddMemoryRequest* AddMemoryRequest = NewObject<UAddMemoryRequest>();

    // If AgentId was not in MemoryCreateData, set it now from BackendAgentId
    FMemoryCreateRequest FinalMemoryData = MemoryCreateData;
    if (FinalMemoryData.AgentId.IsEmpty())
    {
        FinalMemoryData.AgentId = TargetAgentId;
    }
    AddMemoryRequest->SetMemoryData(FinalMemoryData);

    // Bind the completion callback
    AddMemoryRequest->OnRequestCompletedDynamic.AddUniqueDynamic(this, &UAgentCommunicationComponent::OnAddMemorySuccess_Internal);

    // Execute the request via WebServiceSubsystem
    WebServiceSubsystem->ExecuteApiRequest(AddMemoryRequest);
}

void UAgentCommunicationComponent::GenerateAgentReaction(const FAgentReactionRequest& ReactionRequestData)
{
    if (BackendAgentId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("UAgentCommunicationComponent: Cannot generate reaction, BackendAgentId is not set. Agent might not be created yet."));
        return;
    }

    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("UAgentCommunicationComponent::GenerateAgentReaction: WebServiceSubsystem is null"));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Generating reaction for agent %s: Observation=%s, CurrentTime=%s"),
        *BackendAgentId, *ReactionRequestData.Observation, *ReactionRequestData.CurrentTime);

    UGenerateReactionRequest* GenReactionRequest = NewObject<UGenerateReactionRequest>();
    GenReactionRequest->SetRequestData(BackendAgentId, ReactionRequestData);

    // Bind the completion callback
    GenReactionRequest->OnRequestCompletedDynamic.AddUniqueDynamic(this, &UAgentCommunicationComponent::OnGenerateReactionSuccess_Internal);

    // Execute the request via WebServiceSubsystem
    WebServiceSubsystem->ExecuteApiRequest(GenReactionRequest);
}

void UAgentCommunicationComponent::GenerateDialogueResponse(const FAgentReactionRequest& DialogueRequestData)
{
    if (BackendAgentId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("UAgentCommunicationComponent: Cannot generate dialogue, BackendAgentId is not set. Agent might not be created yet."));
        return;
    }

    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("UAgentCommunicationComponent::GenerateDialogueResponse: WebServiceSubsystem is null"));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Generating dialogue for agent %s: Observation=%s, CurrentTime=%s"),
        *BackendAgentId, *DialogueRequestData.Observation, *DialogueRequestData.CurrentTime);

    UGenerateDialogueRequest* GenDialogueRequest = NewObject<UGenerateDialogueRequest>();
    GenDialogueRequest->SetRequestData(BackendAgentId, DialogueRequestData);

    // Bind the completion callback
    GenDialogueRequest->OnRequestCompletedDynamic.AddUniqueDynamic(this, &UAgentCommunicationComponent::OnGenerateDialogueSuccess_Internal);

    // Execute the request via WebServiceSubsystem
    WebServiceSubsystem->ExecuteApiRequest(GenDialogueRequest);
}

FString UAgentCommunicationComponent::GetBackendAgentId() const
{
    return BackendAgentId;
}

void UAgentCommunicationComponent::SetBackendAgentId(const FString& NewBackendAgentId)
{
    BackendAgentId = NewBackendAgentId;
}

bool UAgentCommunicationComponent::HasValidBackendAgentId() const
{
    return !BackendAgentId.IsEmpty();
}

void UAgentCommunicationComponent::OnCreateAgentSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response)
{
    if (bWasSuccessful && StatusCode == 200)
    {
        // Parse the response to get the agent ID
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Extract agent ID from response
            if (JsonObject->HasField(TEXT("id")))
            {
                BackendAgentId = JsonObject->GetStringField(TEXT("id"));

                UE_LOG(LogAgentBridge, Log, TEXT("Agent created successfully with ID: %s"), *BackendAgentId);

                // Broadcast success
                OnBackendAgentCreatedSuccessfully.Broadcast(BackendAgentId);
                return;
            }
            else
            {
                UE_LOG(LogAgentBridge, Error, TEXT("Agent creation response missing 'id' field. Response: %s"), *Response);
            }
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse agent creation response as JSON: %s"), *Response);
        }
    }
    else if (StatusCode == 500)
    {
        // Try to parse error details if available
        TSharedPtr<FJsonObject> ErrorObject;
        TSharedRef<TJsonReader<>> ErrorReader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(ErrorReader, ErrorObject) && ErrorObject.IsValid())
        {
            FString ErrorDetail = TEXT("Unknown error");
            FString ErrorMessage = TEXT("");

            // Extract error details if available
            if (ErrorObject->HasField(TEXT("detail")))
            {
                ErrorDetail = ErrorObject->GetStringField(TEXT("detail"));
            }

            if (ErrorObject->HasField(TEXT("message")))
            {
                ErrorMessage = ErrorObject->GetStringField(TEXT("message"));
                UE_LOG(LogAgentBridge, Error, TEXT("Server error message: %s"), *ErrorMessage);
            }

            UE_LOG(LogAgentBridge, Error, TEXT("Failed to create agent. Server returned 500 error: %s"), *ErrorDetail);
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to create agent. Server returned 500 error with unparseable response: %s"), *Response);
        }
    }
    else
    {
        // Generic error for other status codes
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to create agent. Status: %d, Response: %s"), StatusCode, *Response);
    }

    // Broadcast failure with empty ID
    OnBackendAgentCreationFailed.Broadcast(Response);
}

void UAgentCommunicationComponent::OnAddMemorySuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response)
{
    bool bSuccess = bWasSuccessful && StatusCode == 200;

    if (bSuccess)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("Memory added successfully to agent %s"), *BackendAgentId);
    }
    else
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("Failed to add memory to agent %s. Status: %d"),
            *BackendAgentId, StatusCode);
    }

    // Broadcast the result
    OnMemoryAddedToAgent.Broadcast(bSuccess, Response);
}

void UAgentCommunicationComponent::OnGenerateReactionSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response)
{
    bool bSuccess = bWasSuccessful && StatusCode == 200;

    if (bSuccess)
    {
        // Parse the response to extract the reaction
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            bool bIsDialogue = JsonObject->GetBoolField(TEXT("is_dialogue"));
            FString Reaction = JsonObject->GetStringField(TEXT("reaction"));

            UE_LOG(LogAgentBridge, Log, TEXT("Reaction generated for agent %s: IsDialogue=%s, Reaction=%s"),
                *BackendAgentId, bIsDialogue ? TEXT("True") : TEXT("False"), *Reaction);
        }
        else
        {
            UE_LOG(LogAgentBridge, Log, TEXT("Reaction generated for agent %s (response parsing failed)"),
                *BackendAgentId);
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("Failed to generate reaction for agent %s. Status: %d"),
            *BackendAgentId, StatusCode);
    }

    // Broadcast the result
    OnReactionGenerated.Broadcast(bSuccess, Response);
}

void UAgentCommunicationComponent::OnGenerateDialogueSuccess_Internal(bool bWasSuccessful, int32 StatusCode, const FString& Response)
{
    bool bSuccess = bWasSuccessful && StatusCode == 200;

    if (bSuccess)
    {
        // Parse the response to extract the dialogue
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            bool bIsDialogueContinuing = JsonObject->GetBoolField(TEXT("is_dialogue_continuing"));
            FString DialogueResponse = JsonObject->GetStringField(TEXT("response"));

            UE_LOG(LogAgentBridge, Log, TEXT("Dialogue generated for agent %s: IsContinuing=%s, Response=%s"),
                *BackendAgentId, bIsDialogueContinuing ? TEXT("True") : TEXT("False"), *DialogueResponse);
        }
        else
        {
            UE_LOG(LogAgentBridge, Log, TEXT("Dialogue generated for agent %s (response parsing failed)"),
                *BackendAgentId);
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Warning, TEXT("Failed to generate dialogue for agent %s. Status: %d"),
            *BackendAgentId, StatusCode);
    }

    // Broadcast the result
    OnDialogueResponseGenerated.Broadcast(bSuccess, Response);
}
