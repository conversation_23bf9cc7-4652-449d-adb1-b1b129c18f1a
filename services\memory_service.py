import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from langchain_core.documents import Document

from models import agents_store
from services.agent_service import get_agent
from utils.date_utils import parse_datetime
from config import ENABLE_PERSISTENCE, DEFAULT_USER_ID
from services import persistence_service

logger = logging.getLogger(__name__)

def add_memory(
    agent_id: str, content: str, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[List[str]]:
    """
    Add a memory to an agent.

    Args:
        agent_id: ID of the agent
        content: Content of the memory
        current_time: The time when the memory occurred (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        List of document IDs or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        parts = agent_id.split("-")
        if len(parts) >= 3:
            effective_user_id = parts[-2]
        else:
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to add memory to agent ID: {agent_id}, user_id: {effective_user_id}, content: '{content[:50]}...'")

    agent_data = get_agent(agent_id, effective_user_id) # Use effective_user_id
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for adding memory (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    result = agent.memory.add_memory(content, now=now)
    # logger.info(f"Added memory to agent {agent_id}: {content[:50]}...") # Refine this
    logger.info(f"Service: Memory added successfully to agent ID: {agent_id}, user_id: {effective_user_id}. Result: {result}")

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE:
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id

    return result


def add_memories(
    agent_id: str, contents: List[str], current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[List[str]]:
    """
    Add multiple memories to an agent.

    Args:
        agent_id: ID of the agent
        contents: List of memory contents
        current_time: The time when the memories occurred (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        List of document IDs or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        parts = agent_id.split("-")
        if len(parts) >= 3:
            effective_user_id = parts[-2]
        else:
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to add {len(contents)} memories in bulk to agent ID: {agent_id}, user_id: {effective_user_id}")

    agent_data = get_agent(agent_id, effective_user_id) # Use effective_user_id
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for bulk adding memories (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    memory_content = ";".join(contents)
    result = agent.memory.add_memories(memory_content, now=now)
    # logger.info(f"Added {len(contents)} memories to agent {agent_id}") # Refine this
    logger.info(f"Service: {len(contents)} memories added successfully in bulk to agent ID: {agent_id}, user_id: {effective_user_id}. Result: {result}")

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE:
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id

    return result


def fetch_memories(
    agent_id: str, query: str, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[List[Dict[str, Any]]]:
    """
    Fetch memories from an agent based on a query.

    Args:
        agent_id: ID of the agent
        query: Query to retrieve related memories
        current_time: The current time (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        List of memories or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        parts = agent_id.split("-")
        if len(parts) >= 3:
            effective_user_id = parts[-2]
        else:
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to fetch memories for agent ID: {agent_id}, user_id: {effective_user_id}, query: '{query[:50]}...'")

    agent_data = get_agent(agent_id, effective_user_id) # Use effective_user_id
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for fetching memories (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    memories = agent.memory.fetch_memories(query, now=now)

    # Convert Document objects to dictionaries
    memory_dicts = []
    for memory in memories:
        memory_dicts.append({
            "content": memory.page_content,
            "created_at": memory.metadata.get("created_at", datetime.now()),
            "importance": memory.metadata.get("importance", 0.0),
        })
    logger.info(f"Service: Fetched {len(memory_dicts)} memories for agent ID: {agent_id}, user_id: {effective_user_id}.")
    return memory_dicts


def generate_reflections(
    agent_id: str, current_time: Optional[str] = None, user_id: Optional[str] = None
) -> Optional[List[str]]:
    """
    Generate reflections for an agent based on recent memories.

    Args:
        agent_id: ID of the agent
        current_time: The current time (ISO format)
        user_id: ID of the user who owns the agent (extracted from agent_id if not provided)

    Returns:
        List of reflections or None if agent not found
    """
    effective_user_id = user_id
    if not effective_user_id:
        parts = agent_id.split("-")
        if len(parts) >= 3:
            effective_user_id = parts[-2]
        else:
            effective_user_id = DEFAULT_USER_ID
    else:
        effective_user_id = effective_user_id or DEFAULT_USER_ID
    logger.info(f"Service: Attempting to generate reflections for agent ID: {agent_id}, user_id: {effective_user_id}")

    agent_data = get_agent(agent_id, effective_user_id) # Use effective_user_id
    if not agent_data:
        logger.warning(f"Service: Agent ID {agent_id} not found for generating reflections (user_id: {effective_user_id}).")
        return None

    agent = agent_data["agent"]
    now = parse_datetime(current_time)

    # Force reflection
    agent.memory.reflecting = True
    reflections = agent.memory.pause_to_reflect(now=now)
    agent.memory.reflecting = False

    # logger.info(f"Generated {len(reflections)} reflections for agent {agent_id}") # Refine this
    logger.info(f"Service: Generated {len(reflections)} reflections successfully for agent ID: {agent_id}, user_id: {effective_user_id}.")

    # Persist the updated agent to disk if persistence is enabled
    if ENABLE_PERSISTENCE: # Agent's memory (reflections are memories) definitely changed
        persistence_service.save_agent(agent_data, effective_user_id) # Use effective_user_id

    return reflections
