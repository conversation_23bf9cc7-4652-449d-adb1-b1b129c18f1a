from datetime import datetime
from typing import Optional

def parse_datetime(date_str: Optional[str]) -> Optional[datetime]:
    """
    Parse a datetime string into a datetime object.
    
    Args:
        date_str: Datetime string (ISO format) or None
        
    Returns:
        Datetime object or None if date_str is None
    """
    if not date_str:
        return None
    
    try:
        return datetime.fromisoformat(date_str)
    except ValueError:
        # Try different format if ISO format doesn't work
        try:
            return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S.%fZ")
        except ValueError:
            # If still fails, return current time
            return datetime.now()

def format_datetime(dt: Optional[datetime] = None) -> str:
    """
    Format a datetime object into a string.
    
    Args:
        dt: Datetime object or None (defaults to now)
        
    Returns:
        Formatted datetime string
    """
    if dt is None:
        dt = datetime.now()
    
    return dt.isoformat()

def get_current_time() -> datetime:
    """
    Get the current time.
    
    Returns:
        Current datetime
    """
    return datetime.now()
