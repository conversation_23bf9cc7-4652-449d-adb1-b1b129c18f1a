import logging
import os
from typing import List, Optional
import json # Moved import json to top

from fastapi import APIRouter, HTTPException, Security, Depends, Header, Query, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from services.github_service import github_service # Singleton instance
from utils.logging_utils import log_route_details

logger = logging.getLogger(__name__)
router = APIRouter()

# Optional Backend API Key for securing these endpoints
BACKEND_API_KEY = os.getenv("BACKEND_API_KEY")

async def verify_api_key(authorization: Optional[str] = Header(None)):
    if BACKEND_API_KEY: # Only enforce if BACKEND_API_KEY is configured
        if authorization is None:
            logger.warning("Missing Authorization header when BACKEND_API_KEY is configured.")
            raise HTTPException(status_code=401, detail="Not authenticated: Missing Authorization header.")
        
        scheme, _, credentials = authorization.partition(' ')
        if scheme.lower() != 'bearer' or credentials != BACKEND_API_KEY:
            logger.warning(f"Invalid API Key received. Scheme: {scheme}, Key: {credentials[:10]}...") # Log only a portion of the key
            raise HTTPException(status_code=403, detail="Forbidden: Invalid API Key.")
        logger.info("API Key verified successfully.")
    # If BACKEND_API_KEY is not set, authentication is skipped for these endpoints
    return True


class FileListResponse(BaseModel):
    files: List[str]
    folders: List[str]

@router.get(
    "/list",
    response_model=FileListResponse,
    summary="List files and folders in a GitHub repository path",
    dependencies=[Depends(verify_api_key)] # Apply API key auth if configured
)
@log_route_details(logger) # Added
async def list_repo_contents(request: Request, path: str = Query(default="", description="Path within the GitHub repository. Use empty string for root.")): # Added request
    # logger.info(f"Route (/list): Request received for path: '{path}'") # Covered by decorator
    # try: # Covered by decorator for general exceptions and HTTPExceptions
    # The service expects a path relative to the repo root.
    # Empty string for path in query will be handled by the service.
    files, folders = github_service.list_directory_contents(path)
    logger.info(f"Route (/list): Successfully listed contents for path: '{path}'. Files: {len(files)}, Folders: {len(folders)}") # Keep specific success log
    return FileListResponse(files=files, folders=folders)
    # except HTTPException as http_exc: # Covered by decorator
    #     logger.error(f"Route (/list): HTTPException for path '{path}': {http_exc.status_code} - {http_exc.detail}") # Covered by decorator
    #     raise http_exc # Re-raise to let FastAPI handle it
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Route (/list): Unexpected error for path '{path}': {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Internal server error while listing repository contents.") # Covered by decorator

@router.get(
    "/file",
    response_class=JSONResponse, # Expecting raw JSON content
    summary="Get content of a specific file from GitHub repository",
    dependencies=[Depends(verify_api_key)] # Apply API key auth if configured
)
@log_route_details(logger) # Added
async def get_repo_file_content(request: Request, path: str = Query(..., description="Full path to the file within the GitHub repository.")): # Added request
    # logger.info(f"Route (/file): Request received for file path: '{path}'") # Covered by decorator
    if not path:
        # logger.warning("Route (/file): Received empty path for file content request.") # Decorator will log HTTPException
        raise HTTPException(status_code=400, detail="File path cannot be empty.")
    if not path.endswith(".json"):
        logger.warning(f"Route (/file): Requested file '{path}' is not a .json file. Plugin expects JSON configs.")
        # Allow fetching non-JSON for flexibility, but plugin might not parse it.
        # Consider if this should be a 400 error based on strict plugin requirements.
        # For now, proceed but log.

    # try: # Covered by decorator for general exceptions and HTTPExceptions
    file_content_str = github_service.get_file_content(path)
    # Attempt to parse as JSON to return a proper JSONResponse,
    # as the plugin expects JSON configuration files.
    # import json # Moved to top
    try:
        json_data = json.loads(file_content_str)
        logger.info(f"Route (/file): Successfully fetched and parsed content for file: '{path}'")
        return JSONResponse(content=json_data)
    except json.JSONDecodeError:
        logger.error(f"Route (/file): Content for '{path}' is not valid JSON. Content: {file_content_str[:200]}...")
        # This case is tricky. The plugin expects JSON. If it's not, what should we do?
        # Option 1: Return as text/plain with error status?
        # Option 2: Return 500 because contract (JSON config) is violated?
        # Option 3: Return as JSONResponse with an error structure?
        # For now, let's raise an HTTP 500 as the expectation of JSON content is not met.
        raise HTTPException(status_code=500, detail=f"File content for '{path}' is not valid JSON.") # Specific error, keep raise

    # except HTTPException as http_exc: # Covered by decorator
    #     logger.error(f"Route (/file): HTTPException for file path '{path}': {http_exc.status_code} - {http_exc.detail}") # Covered by decorator
    #     raise http_exc # Re-raise
    # except Exception as e: # Covered by decorator
    #     logger.error(f"Route (/file): Unexpected error for file path '{path}': {e}", exc_info=True) # Covered by decorator
    #     raise HTTPException(status_code=500, detail="Internal server error while getting file content.") # Covered by decorator
