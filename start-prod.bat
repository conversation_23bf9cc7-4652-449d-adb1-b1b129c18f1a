@echo off
REM Quick Start Script for Production-like Testing Environment
REM This script starts the production-like environment with Gunicorn

echo.
echo ========================================
echo  Starting Production-like API (R2)
echo ========================================
echo.

REM Check if Dock<PERSON> is running
docker ps >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Check if .env file exists
if not exist ".env" (
    echo ❌ .env file not found. Please run setup first.
    echo    Run: .\scripts\dev-setup.ps1
    echo.
    pause
    exit /b 1
)

echo ✅ Environment file found
echo.

REM Start the production-like environment
echo 🚀 Starting production-like environment with Gunicorn...
echo    - API will be available at: http://localhost:5001
echo    - API docs will be at: http://localhost:5001/docs
echo    - Health check at: http://localhost:5001/health
echo.
echo 💡 This uses multiple workers like production
echo 💡 Press Ctrl+C to stop the environment
echo.

docker-compose --profile production up api-prod

echo.
echo 🛑 Production-like environment stopped.
echo.
pause
